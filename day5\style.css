/* 现代化样式设计 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
}

.app-header {
    text-align: center;
    color: white;
    margin-bottom: 30px;
}

.app-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.app-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.main-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* 输入区域样式 */
.input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
}

#taskInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

#taskInput:focus {
    outline: none;
    border-color: #667eea;
}

#prioritySelect {
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    cursor: pointer;
}

#addTaskBtn {
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.2s;
}

#addTaskBtn:hover {
    transform: translateY(-2px);
}

/* 统计卡片样式 */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-card i {
    font-size: 2rem;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 过滤器样式 */
.filter-section {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-btn.active,
.filter-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 任务列表样式 */
.task-list {
    min-height: 200px;
}

.task-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s;
}

.task-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.task-item.completed {
    opacity: 0.7;
    background: #e8f5e8;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.task-content {
    flex: 1;
}

.task-text {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.task-text.completed {
    text-decoration: line-through;
    color: #6c757d;
}

.task-meta {
    font-size: 0.9rem;
    color: #6c757d;
}

.priority-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.priority-high {
    background: #ffebee;
    color: #c62828;
}

.priority-medium {
    background: #fff3e0;
    color: #ef6c00;
}

.priority-low {
    background: #e8f5e8;
    color: #2e7d32;
}

.task-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.delete-btn {
    background: #ffebee;
    color: #c62828;
}

.delete-btn:hover {
    background: #c62828;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .input-group {
        flex-direction: column;
    }
    
    .stats-section {
        grid-template-columns: 1fr;
    }
    
    .task-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .task-actions {
        align-self: flex-end;
    }
}