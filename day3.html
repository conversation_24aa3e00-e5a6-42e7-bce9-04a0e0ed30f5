<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Day3 - 数组与对象</title>
    <style>
        /* 基础重置 */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: '微软雅黑', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf9 100%);
            color: #333;
        }
        
        /* 容器布局 */
        .container { 
            display: flex; 
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
            box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
        }
        
        /* 左侧导航样式 */
        .nav { 
            width: 300px; 
            background-color: #2c3e50; 
            color: white;
            padding: 30px 20px;
            position: sticky;
            top: 0;
            height: 100vh;
            overflow-y: auto;
        }
        .nav-title { 
            font-size: 1.6em; 
            font-weight: bold; 
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #4a627a;
        }
        .nav-title .icon { 
            margin-right: 12px; 
            font-size: 1.2em;
            background: #3498db;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .learning-content { margin-top: 20px; }
        .learning-item { 
            display: block; 
            padding: 14px 18px; 
            border-radius: 8px; 
            cursor: pointer; 
            margin-bottom: 8px;
            transition: all 0.3s;
            font-size: 1.1em;
            text-decoration: none;
            color: #ecf0f1;
            position: relative;
        }
        .learning-item:hover { 
            background: #34495e; 
            transform: translateX(5px);
        }
        .learning-item.active {
            background: #3498db;
            font-weight: bold;
        }
        .learning-item .icon { 
            margin-right: 12px; 
            min-width: 24px;
            display: inline-block;
            text-align: center;
        }
        .sub-item { 
            display: block; 
            padding: 12px 15px 12px 55px; 
            font-size: 0.95em;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s;
            border-radius: 6px;
            margin-bottom: 4px;
        }
        .sub-item:hover { 
            background: rgba(52, 152, 219, 0.15); 
            color: white;
        }
        
        /* 右侧内容样式 */
        .content { 
            flex: 1; 
            padding: 40px 60px; 
            background: white;
            overflow-y: auto;
        }
        .section { 
            margin-bottom: 60px; 
            padding-top: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 40px;
        }
        .section:last-child { border-bottom: none; }
        .section h2 { 
            font-size: 1.9em; 
            color: #2c3e50; 
            margin-bottom: 25px; 
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
            display: inline-block;
        }
        .explain { 
            font-size: 1.1em; 
            color: #444; 
            line-height: 1.8; 
            margin-bottom: 20px;
            background: #f9fbfd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .explain strong { color: #2c3e50; }
        .explain ul {
            margin: 15px 0 15px 30px;
        }
        .explain ul li {
            margin-bottom: 10px;
        }
        
        /* 代码示例样式 - 修复了对比度问题 */
        .code-example { 
            background: #2c3e50; 
            border-radius: 8px; 
            padding: 20px; 
            margin-bottom: 25px;
            overflow-x: auto;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .code-example pre { 
            margin: 0; 
            font-size: 1.05em; 
            line-height: 1.5;
            font-family: 'Fira Code', 'Courier New', monospace;
            color: #f8f8f8; /* 增加整体文本亮度 */
        }
        /* 提高注释颜色对比度 */
        .code-comment { color: #b0bdc5; } /* 修改为更亮的灰色 */
        .code-keyword { color: #ff7b89; } /* 更亮的红色 */
        .code-function { color: #79c0ff; } /* 更亮的蓝色 */
        .code-string { color: #a5d6a7; } /* 更亮的绿色 */
        .code-variable { color: #ffd700; } /* 更亮的黄色 */
        .code-number { color: #d9a9ff; } /* 更亮的紫色 */
        .code-property { color: #4dc7b0; } /* 更亮的青色 */
        
        /* 导航链接样式 */
        .next-day-link {
            display: block;
            margin-top: 40px;
            padding: 15px 25px;
            text-align: center;
            background: linear-gradient(90deg, #3498db, #2c3e50);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1.2em;
            transition: all 0.3s;
        }
        .next-day-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .container {
                flex-direction: column;
            }
            .nav {
                width: 100%;
                height: auto;
                position: relative;
            }
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
            scroll-padding-top: 30px;
        }

        /* 交互式代码演示样式 */
        .code-demo {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e1e4e8;
        }
        .demo-result {
            margin-top: 15px;
            padding: 15px;
            background: #2c3e50;
            color: #f0f0f0; /* 调整为更亮的文本 */
            border-radius: 6px;
            font-family: monospace;
            font-weight: bold; /* 增加粗体提高可读性 */
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧导航 -->
        <div class="nav">
            <div class="nav-title">
                <span class="icon">3</span>
                <span>数组与对象</span>
            </div>
            <div class="learning-content">
                <a href="#array-basics" class="learning-item active">
                    <span class="icon">▶</span>
                    <span>数组基础</span>
                </a>
                <a href="#creating-arrays" class="sub-item">创建数组</a>
                <a href="#accessing-elements" class="sub-item">访问元素</a>
                <a href="#array-methods" class="sub-item">数组方法</a>
                
                <a href="#object-basics" class="learning-item">
                    <span class="icon">▶</span>
                    <span>对象基础</span>
                </a>
                <a href="#object-creation" class="sub-item">对象创建</a>
                <a href="#object-properties" class="sub-item">属性与方法</a>
                <a href="#object-iteration" class="sub-item">对象遍历</a>
                
                <a href="#array-operations" class="learning-item">
                    <span class="icon">▶</span>
                    <span>数组操作</span>
                </a>
                <a href="#iteration-methods" class="sub-item">迭代方法</a>
                <a href="#transformation-methods" class="sub-item">转换方法</a>
                <a href="#search-methods" class="sub-item">搜索方法</a>
                
                <a href="#object-operations" class="learning-item">
                    <span class="icon">▶</span>
                    <span>对象操作</span>
                </a>
                <a href="#destructuring" class="sub-item">解构赋值</a>
                <a href="#spread-operator" class="sub-item">展开运算符</a>
                <a href="#object-methods" class="sub-item">对象方法</a>
                
                <a href="day4.html" class="next-day-link">前往Day4：DOM操作 →</a>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="content">
            <!-- 数组基础 section -->
            <div class="section" id="array-basics">
                <h2>一、数组基础 - 创建与访问</h2>
                
                <div class="sub-section" id="creating-arrays">
                    <h3>1. 创建数组</h3>
                    <div class="explain">
                        数组是JavaScript中用于存储<strong>有序集合</strong>的数据结构，可以包含不同类型的元素。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 使用数组字面量创建（推荐）</span>
<span class="code-keyword">const</span> fruits = [<span class="code-string">'苹果'</span>, <span class="code-string">'香蕉'</span>, <span class="code-string">'橙子'</span>];

<span class="code-comment">// 使用Array构造函数</span>
<span class="code-keyword">const</span> numbers = <span class="code-keyword">new</span> <span class="code-function">Array</span>(1, 2, 3);

<span class="code-comment">// 创建空数组</span>
<span class="code-keyword">const</span> emptyArray = [];

<span class="code-comment">// 创建多维数组</span>
<span class="code-keyword">const</span> matrix = [
    [1, 2, 3],
    [4, 5, 6],
    [7, 8, 9]
];</pre>
                    </div>
                </div>

                <div class="sub-section" id="accessing-elements">
                    <h3>2. 访问数组元素</h3>
                    <div class="explain">
                        使用<strong>索引</strong>访问数组元素（索引从0开始），使用<code>length</code>属性获取数组长度。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">const</span> colors = [<span class="code-string">'红'</span>, <span class="code-string">'绿'</span>, <span class="code-string">'蓝'</span>];

<span class="code-comment">// 获取第一个元素</span>
console.log(colors[0]); <span class="code-comment">// 输出: 红</span>

<span class="code-comment">// 获取最后一个元素</span>
console.log(colors[colors.length - 1]); <span class="code-comment">// 输出: 蓝</span>

<span class="code-comment">// 修改数组元素</span>
colors[1] = <span class="code-string">'黄'</span>;
console.log(colors); <span class="code-comment">// 输出: ["红", "黄", "蓝"]</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="array-methods">
                    <h3>3. 常用数组方法</h3>
                   极客教程
                    <div class="code-example">
                        <pre><span class="code-keyword">let</span> arr = [<span class="code-string">'A'</span>, <span class="code-string">'B'</span>];

<span class="code-comment">// 添加/删除元素</span>
arr.<span class="code-function">push</span>(<span class="code-string">'C'</span>); <span class="code-comment">// 末尾添加: ['A', 'B', 'C']</span>
arr.<span class="code-function">pop</span>();      <span class="code-comment">// 删除末尾: ['A', 'B']</span>
arr.<span极客教程="code-function">unshift</span>(<span class="code-string">'Z'</span>); <span class="code-comment">// 开头添加: ['Z', 'A', 'B']</span>
arr.<span class="code-function">shift</span>();     <span class="code-comment">// 删除开头: ['A', 'B']</span>

<span class="code-comment">// 连接数组</span>
<span class="code-keyword">const</span> newArr = arr.<span class="code-function">concat</span>([<span class="code-string">'C'</span>, <span class="code-string">'D'</span>]); <span class="code-comment">// ['A', 'B', 'C', 'D']</span>

<span class="code-comment">// 翻转数组</span>
arr.<span class="code-function">reverse</span>(); <span class="code-comment">// ['B', 'A']</span></pre>
                    </div>
                </div>
            </div>

            <!-- 对象基础 section -->
            <div class="section" id="object-basics">
                <h2>二、对象基础 - 属性与方法</h2>
                
                <div class="sub-section" id="object-creation">
                    <h3>1. 对象创建</h3>
                    <div class="explain">
                        对象是键值对的集合，用于存储和组织相关数据与功能。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 对象字面量（推荐）</span>
<span class="code-keyword">const</span> person = {
    name: <span class="code-string">'张三'</span>,
    age: 25,
    isStudent: true
};

<span class="code-comment">// 使用构造函数</span>
<span class="code-keyword">const</span> car = <span class="code-keyword">new</span> <span class="code-function">Object</span>();
car.brand = <span class="code-string">'Toyota'</span>;
car.model = <span class="code-string">'Camry'</span>;
car.year = 2020;

<span class="code-comment">// 嵌套对象</span>
<span class="code-keyword">const</span> company = {
    name: <span class="code-string">'TechCorp'</span>,
    employees: [
        { name: <span class="code-string">'Alice'</span>, position: <span class="code-string">'Developer'</span> },
        { name: <span class="code-string">'Bob'</span>, position: <span class="code-string">'Designer'极客教程}
    ],
    address: {
        city: <span class="code-string">'北京'</span>,
        street: <span class="code-string">'中关村大街'</span>
    }
};</pre>
                    </div>
                </div>

                <div class="sub-section" id="object-properties">
                    <h3>2. 属性与方法</h3>
                    <div class="explain">
                        对象包含属性（数据）和方法（函数），使用点号或方括号访问。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">const</span> student = {
    <span class="code-comment">// 属性</span>
    name: <span class="code-string">'李四'</span>,
    grade: 85,
    
    <span class="code-comment">// 方法</span>
    getGrade: <span class="code-keyword">function</span>() {
        <span class="code-keyword">return</span> this.grade;
    },
    
    <span class="code-comment">// ES6简写方法</span>
    updateGrade(newGrade) {
        this.grade = newGrade;
        <span class="code-keyword">return</span> this.grade;
    }
};

<span class="code-comment">// 访问属性</span>
console.log(student.name); <span class="code-comment">// "李四"</span>
console.log(student[<span class="code-string">'grade'</span>]); <span class="code-comment">// 85</span>

<span class="code-comment">// 调用方法</span>
console.log(student.getGrade()); <span class="code-comment">// 85</span>
console.log(student.updateGrade(90)); <span class="code-comment">// 90</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="object-iteration">
                    <h3>3. 对象遍历</h3>
                    <div class="explain">
                        遍历对象属性：<code>for...in</code>循环、<code>Object.keys()</code>、<code>Object.values()</code>和<code>Object.entries()</code>。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">const</span> user = {
    id: 1,
    username: <span class="code-string">'js_dev'</span>,
    email: <span class="code-string">'<EMAIL>'</span>,
    isActive: true
};

<span class="code-comment">// for...in 循环</span>
<span class="code-keyword">for</span> (<span class="code-keyword">let</span> key in user) {
    console.log(`${key}: ${user[key]}`);
}

<span class="code-comment">// Object.keys()</span>
<span class="code-keyword">const</span> keys = Object.keys(user);
console.log(keys); <span class="code-comment">// ["id", "username", "email", "isActive"]</span>

<span class="code-comment">// Object.values()</span>
<span class="code-keyword">const</span> values = Object.values(user);
console.log(values); <span class="code-comment">// [1, "js_dev", "<EMAIL>", true]</span>

<span class="code-comment">// Object.entries()</span>
<span class="code-keyword">const</span> entries = Object.entries(user);
console.log(entries); 
<span class="code-comment">// 输出: [["id", 1], ["username", "js_dev"], ...]</span></pre>
                    </div>
                </div>
            </div>

            <!-- 数组操作 section -->
            <div class="section" id="array-operations">
                <h2>三、数组操作 - 迭代与转换</h2>
                
                <div class="sub-section" id="iteration-methods">
                    <h3>1. 迭代方法</h3>
                    <div class="explain">
                        数组提供多种迭代方法，无需手动编写循环：
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">const</span> numbers = [1, 2, 3, 4, 5];

<span class="code-comment">// forEach - 遍历数组</span>
numbers.forEach(num => console.log(num * 2));

<span class="code-comment">// map - 创建新数组</span>
<span class="code-keyword">const</span> doubled = numbers.map(num => num * 2);
console.log(doubled); <span class="code-comment">// [2, 4, 6, 8, 10]</span>

<span class="code-comment">// filter - 过滤元素</span>
<span class="code-keyword">const</span> evens = numbers.filter(num => num % 2 === 0);
console.log(evens); <span class="code-comment">// [2, 4]</span>

<span class="code-comment">// reduce - 累积计算</span>
<span class="code-keyword">const</span> sum = numbers.reduce((total, num) => total + num, 0);
console.log(sum); <span class="code-comment">// 15</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="transformation-methods">
                    <h3>2. 转换方法</h3>
                    <div class="explain">
                        数组与字符串之间的转换：
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">const</span> fruits = [<span class="code-string">'苹果'</span>, <span class="code-string">'香蕉'</span>, <span class="code-string">'橙子'</span>];

<span class="code-comment">// join - 数组转字符串</span>
<span class="code-keyword">const</span> fruitString = fruits.join(<span class="code-string">', '</span>);
console.log(fruitString); <span class="code-comment">// "苹果, 香蕉, 橙子"</span>

<span class="code-comment">// split - 字符串转数组</span>
<span class="code-keyword">const</span> fruitArray = fruitString.split(<span class="code-string">', '</span>);
console.log(fruitArray); <span class="code-comment">// ["苹果", "香蕉", "橙子"]</span>

<span class="code-comment">// toString - 简单转换</span>
console.log(fruits.toString()); <span class="code-comment">// "苹果,香蕉,橙子"</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="search-methods">
                    <h3>3. 搜索方法</h3>
                    <div class="explain">
                        在数组中查找特定元素：
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">const</span> tech = [<span class="code-string">'HTML'</span>, <span class="code-string">'CSS'</span>, <span class="code-string">'JavaScript'</span>, <span class="code-string">'React'</span>];

<span class="code-comment">// indexOf - 查找元素索引</span>
console.log(tech.indexOf(<span class="code-string">'CSS'</span>)); <span class="code-comment">// 1</span>

<span class="code-comment">// includes - 检查是否存在</span>
console.log(tech.includes(<span class="code-string">'JavaScript'</span>)); <span class="code-comment">// true</span>

<span class="code-comment">// find - 查找符合条件的第一个元素</span>
<span class="code-keyword">const</span> longTech = tech.find(item => item.length > 4);
console.log(longTech); <span class="code-comment">// "JavaScript"</span>

<span class="code-comment">// findIndex - 查找符合条件的第一个元素的索引</span>
<span class="code-keyword">const</span> longTechIndex = tech.findIndex(item => item.length > 4);
console.log(longTechIndex); <span class="code-comment">// 2</span>

<span class="code-comment">// some - 检查是否有元素符合条件</span>
console.log(tech.some(item => item.startsWith(<span class="code-string">'R'</span>))); <span class="code-comment">// true</span>

<span class="code-comment">// every - 检查所有元素是否符合条件</span>
console.log(tech.every(item => item.length > 2)); <span class="code-comment">// true</span></pre>
                    </div>
                </div>
            </div>

            <!-- 对象操作 section -->
            <div class="section" id="object-operations">
                <h2>四、对象操作 - 解构与展开</h2>
                
                <div class="sub-section" id="destructuring">
                    <h3>1. 解构赋值</h3>
                    <div class="explain">
                        从对象或数组中提取数据的简便方法：
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 对象解构</span>
<span class="code-keyword">const</span> user = { 
    id: 101, 
    name: <span class="code-string">'王五'</span>, 
    email: <span class="code-string">'<EMAIL>'</span> 
};

<span class="code-keyword">const</span> { name, email } = user;
console.log(name, email); <span class="极客教程">// "王五" "<EMAIL>"</span>

<span class="code-comment">// 重命名解构变量</span>
<span class="code-keyword">const</span> { name: userName, email: userEmail } = user;

<span class="code-comment">// 数组解构</span>
<span class="code-keyword">const</span> rgb = [255, 120, 80];
<span class="code-keyword">const</span> [red, green, blue] = rgb;
console.log(red, green, blue); <span class="code-comment">// 255 120 80</span>

<span class="code-comment">// 跳过元素</span>
<span class="code-keyword">const</span> [first, , third] = [<span class="code-string">'A'</span>, <span class="code-string">'B'</span>, <span class="code-string">'C'</极客教程>];
console.log(first, third); <span class="code-comment">// "A" "C"</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="spread-operator">
                    <h3>2. 展开运算符</h3>
                    <div class="explain">
                        展开运算符(<code>...</code>)用于复制或合并数组/对象：
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 复制数组</span>
<span class="code-keyword">const</span> original = [1, 2, 3];
<span class="code-keyword">const</span> copy = [...original];

<span class="code-comment">// 合并数组</span>
<span class="code-keyword">const</span> arr1 = [<span class="code-string">'A'</span>, <span class="code-string">'B'</span>];
<span class="code-keyword">const</span> arr2 = [<span class="code-string">'C'</span>, <span class="code-string">'D'</span>];
<span class="code-keyword">const</span> combined = [...arr1, ...arr2]; <span class="code-comment">// ["A", "B", "C", "D"]</span>

<span class="code-comment">// 复制对象</span>
<span class="code-keyword">const</span> originalObj = { a: 1, b: 2 };
<span class="code-keyword">const</span> copyObj = { ...originalObj };

<span class="code-comment">// 合并对象</span>
<span class="code-keyword">const</span> obj1 = { a: 1, b: 2 };
<span class="code-keyword">const</span> obj2 = { c: 3, d: 4 };
<span class="code-keyword">const</span> merged = { ...obj1, ...obj2 }; <span class="code-comment">// { a: 1, b: 2, c: 3, d: 4 }</span>

<span class="code-comment">// 更新属性</span>
<span class="code-keyword">const</span> updated = { ...originalObj, b: 20 }; <span class="code-comment">// { a: 1, b: 20 }</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="object-methods">
                    <h3>3. 对象方法</h3>
                    <div class="explain">
                        Object的静态方法用于对象操作：
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">const</span> obj = { a: 1, b: 2, c: 3 };

<span class="code-comment">// Object.assign - 合并对象</span>
<span class="code-keyword">const</span> target = { a: 1 };
<span class="code-keyword">const</span> source = { b: 2 };
<span class="code-keyword">const</span> result = Object.assign(target, source);
console.log(result); <span class="code-comment">// { a: 1, b: 2 }</span>

<span class="code-comment">// Object.freeze - 冻结对象（不可修改）</span>
<span class="code-keyword">const</span> frozen = Object.freeze(obj);
frozen.a = 10; <span class="code-comment">// 无效（严格模式会报错）</span>

<span class="code-comment">// Object.seal - 密封对象（不可添加/删除属性）</span>
<span class="code-keyword">const</span> sealed = Object.seal(obj);
sealed.d = 4; <span class="code-comment">// 无效</span>
delete sealed.a; <span class="code-comment">// 无效</span>

<span class="code-comment">// Object.create - 创建原型对象</span>
<span class="code-keyword">const</span> personPrototype = {
    greet() {
        console.log(<span class="code-string">`你好，我是<span class="code-variable">${this.name}</span>`</span>);
    }
};

<span class="code-keyword">const</span> me = Object.create(personPrototype);
me.name = <span class="code-string">'小明'</span>;
me.greet(); <span class="code-comment">// "你好，我是小明"</span></pre>
                    </div>
                </div>
            </div>

            <!-- 交互式代码演示 -->
            <div class="section">
                <h2>代码练习</h2>
                <div class="explain">
                    尝试在下面的交互区域练习数组与对象操作：
                </div>
                
                <div class="code-demo">
                    <h3>对象操作演示</h3>
                    <pre><span class="code-keyword">const</span> user = {
    id: 1001,
    name: <span class="code-string">'张三'</span>,
    email: <span class="code-string">'<EMAIL>'</span>,
    roles: [<span class="code-string">'admin'</span>, <span class="code-string">'editor'</span>]
};

<span class="code-comment">// 尝试添加新属性</span>
user.age = 30;

<span class="code-comment">// 尝试修改属性</span>
user.email = <span class="code-string">'<EMAIL>'</span>;

<span class="code-comment">// 尝试添加角色</span>
user.roles.push(<span class="code-string">'tester'</span>);

console.log(user);</pre>
                    
                    <div class="demo-result">
                        <span class="code-property">输出结果：</span><br>
                        {<br>
                        &nbsp;&nbsp;id: 1001,<br>
                        &nbsp;&nbsp;name: "张三",<br>
                        &nbsp;&nbsp;email: "<EMAIL>",<br>
                        &nbsp;&nbsp;roles: ["admin", "editor", "tester"],<br>
                        &nbsp;&nbsp;age: 30<br>
                        }
                    </div>
                </div>
                
                <div class="code-demo">
                    <h3>数组操作演示</h3>
                    <pre><span class="code-keyword">const</span> numbers = [10, 20, 30, 40, 50];

<span class="code-comment">// 使用map创建新数组</span>
<span class="code-keyword">const</span> squares = numbers.map(num => num * num);

<span class="code-comment">// 使用filter筛选元素</span>
<span class="code-keyword">const</span> bigNumbers = numbers.filter(num => num > 25);

<span class="code-comment">// 使用reduce计算总和</span>
<span class="code-keyword">const</span> total = numbers.reduce((sum, num) => sum + num, 0);

console.log(<span class="code-string">"平方值："</span>, squares);<br>
console.log(<span class="code-string">"大于25的数："</span>, bigNumbers);<br>
console.log(<span class="code-string">"总和："</span>, total);</pre>
                    
                    <div class="demo-result">
                        <span class="code-property">输出结果：</span><br>
                        平方值: [100, 400, 900, 1600, 2500]<br>
                        大于25的数: [30, 40, 50]<br>
                        总和: 150
                    </div>
                </div>
            </div>

            <!-- 跳转到day4的链接 -->
            <a href="day4.html" class="next-day-link">前往Day4：DOM操作 →</a>
        </div>
    </div>

    <script>
        // 简单的导航激活状态切换
        document.querySelectorAll('.learning-item, .sub-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.learning-item').forEach(i => i.classList.remove('active'));
                
                // 如果点击的是子项，激活其父级
                if(this.classList.contains('sub-item')) {
                    this.parentNode.previousElementSibling.classList.add('active');
                } else {
                    this.classList.add('active');
                }
            });
        });
        
        // 自动滚动到顶部
        window.addEventListener('load', () => {
            if(window.location.hash) {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            }
        });
    </script>
</body>
</html>
