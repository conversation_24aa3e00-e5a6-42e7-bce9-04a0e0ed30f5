// Task.js - 使用ES6+ Class语法定义任务类
export default class Task {
    // 使用私有字段（ES2022特性）
    #id;
    #createdAt;
    
    constructor(text, priority = 'medium') {
        // 使用Symbol创建唯一ID（ES6特性）
        this.#id = Symbol('task-id');
        this.text = text;
        this.priority = priority;
        this.completed = false;
        this.#createdAt = new Date();
    }
    
    // Getter方法 - 获取任务ID
    get id() {
        return this.#id;
    }
    
    // Getter方法 - 获取创建时间
    get createdAt() {
        return this.#createdAt;
    }
    
    // 切换任务完成状态
    toggleComplete() {
        this.completed = !this.completed;
        return this;
    }
    
    // 更新任务文本
    updateText(newText) {
        if (newText && newText.trim()) {
            this.text = newText.trim();
        }
        return this;
    }
    
    // 更新优先级
    updatePriority(newPriority) {
        const validPriorities = ['low', 'medium', 'high'];
        if (validPriorities.includes(newPriority)) {
            this.priority = newPriority;
        }
        return this;
    }
    
    // 使用模板字符串生成HTML（ES6特性）
    toHTML() {
        const priorityText = {
            low: '低',
            medium: '中', 
            high: '高'
        };
        
        // 使用模板字符串和表达式插值
        return `
            <div class="task-item ${this.completed ? 'completed' : ''}" data-task-id="${this.id.toString()}">
                <input type="checkbox" class="task-checkbox" ${this.completed ? 'checked' : ''}>
                <div class="task-content">
                    <div class="task-text ${this.completed ? 'completed' : ''}">${this.text}</div>
                    <div class="task-meta">
                        <span class="priority-badge priority-${this.priority}">
                            ${priorityText[this.priority]}优先级
                        </span>
                        <span>创建于: ${this.#createdAt.toLocaleString()}</span>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="action-btn delete-btn" data-action="delete">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        `;
    }
    
    // 静态方法 - 从JSON数据创建任务
    static fromJSON(data) {
        const task = new Task(data.text, data.priority);
        task.completed = data.completed;
        return task;
    }
    
    // 转换为JSON格式（用于存储）
    toJSON() {
        return {
            text: this.text,
            priority: this.priority,
            completed: this.completed,
            createdAt: this.#createdAt.toISOString()
        };
    }
}