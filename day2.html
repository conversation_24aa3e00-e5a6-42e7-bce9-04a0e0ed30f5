<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Day2 - 函数与作用域</title>
    <style>
        /* 基础重置 */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: '微软雅黑', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf9 100%);
            color: #333;
        }
        
        /* 容器布局 */
        .container { 
            display: flex; 
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
            box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
        }
        
        /* 左侧导航样式 */
        .nav { 
            width: 300px; 
            background-color: #2c3e50; 
            color: white;
            padding: 30px 20px;
            position: sticky;
            top: 0;
            height: 100vh;
            overflow-y: auto;
        }
        .nav-title { 
            font-size: 1.6em; 
            font-weight: bold; 
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #4a627a;
        }
        .nav-title .icon { 
            margin-right: 12px; 
            font-size: 1.2em;
            background: #3498db;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .learning-content { margin-top: 20px; }
        .learning-item { 
            display: block; 
            padding: 14px 18px; 
            border-radius: 8px; 
            cursor: pointer; 
            margin-bottom: 8px;
            transition: all 0.3s;
            font-size: 1.1em;
            text-decoration: none;
            color: #ecf0f1;
            position: relative;
        }
        .learning-item:hover { 
            background: #34495e; 
            transform: translateX(5px);
        }
        .learning-item.active {
            background: #3498db;
            font-weight: bold;
        }
        .learning-item .icon { 
            margin-right: 12px; 
            min-width: 24px;
            display: inline-block;
            text-align: center;
        }
        .sub-item { 
            display: block; 
            padding: 12px 15px 12px 55px; 
            font-size: 0.95em;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s;
            border-radius: 6px;
            margin-bottom: 4px;
        }
        .sub-item:hover { 
            background: rgba(52, 152, 219, 0.15); 
            color: white;
        }
        
        /* 右侧内容样式 */
        .content { 
            flex: 1; 
            padding: 40px 60px; 
            background: white;
            overflow-y: auto;
        }
        .section { 
            margin-bottom: 60px; 
            padding-top: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 40px;
        }
        .section:last-child { border-bottom: none; }
        .section h2 { 
            font-size: 1.9em; 
            color: #2c3e50; 
            margin-bottom: 25px; 
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
            display: inline-block;
        }
        .explain { 
            font-size: 1.1em; 
            color: #444; 
            line-height: 1.8; 
            margin-bottom: 20px;
            background: #f9fbfd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .explain strong { color: #2c3e50; }
        .explain ul {
            margin: 15px 0 15px 30px;
        }
        .explain ul li {
            margin-bottom: 10px;
        }
        
        /* 代码示例样式 */
        .code-example { 
            background: #2c3e50; 
            border-radius: 8px; 
            padding: 20px; 
            margin-bottom: 25px;
            overflow-x: auto;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .code-example pre { 
            margin: 0; 
            font-size: 1.05em; 
            line-height: 1.5;
            font-family: 'Fira Code', 'Courier New', monospace;
            color: #ffffff;
        }
        .code-comment { color: #ffffff; }
        .code-keyword { color: #e74c3c; }
        .code-function { color: #3498db; }
        .code-string { color: #2ecc71; }
        .code-variable { color: #f39c12; }
        .code-number { color: #9b59b6; }
        
        /* 导航链接样式 */
        .next-day-link {
            display: block;
            margin-top: 40px;
            padding: 15px 25px;
            text-align: center;
            background: linear-gradient(90deg, #3498db, #2c3e50);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1.2em;
            transition: all 0.3s;
        }
        .next-day-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .container {
                flex-direction: column;
            }
            .nav {
                width: 100%;
                height: auto;
                position: relative;
            }
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
            scroll-padding-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧导航 -->
        <div class="nav">
            <div class="nav-title">
                <span class="icon">2</span>
                <span>函数与作用域</span>
            </div>
            <div class="learning-content">
                <a href="#function-definition" class="learning-item active">
                    <span class="icon">▶</span>
                    <span>函数定义</span>
                </a>
                <a href="#function-declaration" class="sub-item">函数声明</a>
                <a href="#function-expression" class="sub-item">函数表达式</a>
                <a href="#arrow-function" class="sub-item">箭头函数</a>
                
                <a href="#parameters" class="learning-item">
                    <span class="icon">▶</span>
                    <span>参数传递</span>
                </a>
                <a href="#default-params" class="sub-item">默认参数</a>
                <a href="#rest-params" class="sub-item">剩余参数</a>
                
                <a href="#scope" class="learning-item">
                    <span class="icon">▶</span>
                    <span>作用域</span>
                </a>
                <a href="#global-scope" class="sub-item">全局作用域</a>
                <a href="#function-scope" class="sub-item">函数作用域</a>
                <a href="#block-scope" class="sub-item">块级作用域</a>
                
                <a href="#closure" class="learning-item">
                    <span class="icon">▶</span>
                    <span>闭包</span>
                </a>
                <a href="#closure-concept" class="sub-item">概念理解</a>
                <a href="#closure-example" class="sub-item">实际应用</a>
                
                <a href="day3.html" class="next-day-link">前往Day3：数组与对象 →</a>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="content">
            <!-- 函数定义 section -->
            <div class="section" id="function-definition">
                <h2>一、函数定义 - 三种方式</h2>
                
                <div class="sub-section" id="function-declaration">
                    <h3>1. 函数声明（Function Declaration）</h3>
                    <div class="explain">
                        使用<strong>function关键字</strong>直接声明函数，具有<strong>变量提升（Hoisting）</strong>特性（函数会被提升到当前作用域顶部，可在声明前调用）。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 函数声明（可在声明前调用）</span>
<span class="code-function">sayHello</span>(<span class="code-string">'Alice'</span>); <span class="code-comment">// 输出: Hello, Alice!</span>

<span class="code-keyword">function</span> <span class="code-function">sayHello</span>(name) {
    console.log(<span class="code-string">`Hello, ${name}!`</span>);
}</pre>
                    </div>
                </div>

                <div class="sub-section" id="function-expression">
                    <h3>2. 函数表达式（Function Expression）</h3>
                    <div class="explain">
                        将函数赋值给变量，<strong>无变量提升</strong>（必须先定义后调用），通常用于匿名函数或回调函数。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 函数表达式（不能在声明前调用）</span>
<span class="code-keyword">const</span> sayHi = <span class="code-keyword">function</span>(name) {
    console.log(<span class="code-string">`Hi, ${name}!`</span>);
};

<span class="code-function">sayHi</span>(<span class="code-string">'Bob'</span>); <span class="code-comment">// 输出: Hi, Bob!</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="arrow-function">
                    <h3>3. 箭头函数（Arrow Function）</h3>
                    <div class="explain">
                        ES6新增语法，简化函数写法，<strong>无自己的this</strong>（继承外层作用域的this），适合短函数或回调函数。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 完整语法</span>
<span class="code-keyword">const</span> add = (a, b) => {
    <span class="code-keyword">return</span> a + b;
};

<span class="code-comment">// 简化语法（单表达式可省略{}和return）</span>
<span class="code-keyword">const</span> multiply = (a, b) => a * b;

<span class="code-comment">// 单参数可省略()</span>
<span class="code-keyword">const</span> sayBye = name => console.log(<span class="code-string">`Bye, ${name}!`</span>);

console.log(<span class="code-function">add</span>(1,2)); <span class="code-comment">// 输出: 3</span>
console.log(<span class="code-function">multiply</span>(3,4)); <span class="code-comment">// 输出: 12</span>
<span class="code-function">sayBye</span>(<span class="code-string">'Charlie'</span>); <span class="code-comment">// 输出: Bye, Charlie!</span></pre>
                    </div>
                </div>
            </div>

            <!-- 参数传递 section -->
            <div class="section" id="parameters">
                <h2>二、参数传递 - 默认与剩余参数</h2>
                
                <div class="sub-section" id="default-params">
                    <h3>1. 默认参数（Default Parameters）</h3>
                    <div class="explain">
                        为函数参数指定默认值，当未传递该参数或传递undefined时，使用默认值。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">function</span> <span class="code-function">greet</span>(name = <span class="code-string">'Guest'</span>, age = 18) {
    console.log(<span class="code-string">`Hello, ${name}! You are ${age} years old.`</span>);
}

<span class="code-function">greet</span>(); <span class="code-comment">// 输出: Hello, Guest! You are 18 years old.</span>
<span class="code-function">greet</span>(<span class="code-string">'David'</span>); <span class="code-comment">// 输出: Hello, David! You are 18 years old.</span>
<span class="code-function">greet</span>(<span class="code-string">'Eve'</span>, 20); <span class="code-comment">// 输出: Hello, Eve! You are 20 years old.</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="rest-params">
                    <h3>2. 剩余参数（Rest Parameters）</h3>
                    <div class="explain">
                        使用<strong>...</strong>收集函数的剩余参数，返回一个数组，用于处理不定数量的参数。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">function</span> <span class="code-function">sum</span>(...numbers) {
    <span class="code-keyword">return</span> numbers.<span class="code-function">reduce</span>((total, num) => total + num, 0);
}

console.log(<span class="code-function">sum</span>(1,2,3)); <span class="code-comment">// 输出: 6</span>
console.log(<span class="code-function">sum</span>(4,5,6,7)); <span class="code-comment">// 输出: 22</span>
console.log(<span class="code-function">sum</span>()); <span class="code-comment">// 输出: 0</span></pre>
                    </div>
                </div>
            </div>

            <!-- 作用域 section -->
            <div class="section" id="scope">
                <h2>三、作用域 - 变量的可访问范围</h2>
                
                <div class="sub-section" id="global-scope">
                    <h3>1. 全局作用域（Global Scope）</h3>
                    <div class="explain">
                        在函数外声明的变量，<strong>整个文档可访问</strong>，浏览器中属于window对象的属性。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-comment">// 全局变量</span>
<span class="code-keyword">let</span> globalVar = <span class="code-string">'I am global'</span>;

<span class="code-keyword">function</span> <span class="code-function">showGlobal</span>() {
    console.log(globalVar); <span class="code-comment">// 可访问全局变量</span>
}

<span class="code-function">showGlobal</span>(); <span class="code-comment">// 输出: I am global</span>
console.log(window.globalVar); <span class="code-comment">// 输出: I am global（浏览器环境）</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="function-scope">
                    <h3>2. 函数作用域（Function Scope）</h3>
                    <div class="explain">
                        在函数内声明的变量（用var/let/const），<strong>仅函数内可访问</strong>，函数外无法访问。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">function</span> <span class="code-function">showLocal</span>() {
    <span class="code-keyword">let</span> localVar = <span class="code-string">'I am local'</span>; <span class="code-comment">// 函数内变量</span>
    console.log(localVar); <span class="code-comment">// 输出: I am local</span>
}

<span class="code-function">showLocal</span>();
console.log(localVar); <span class="code-comment">// 报错: ReferenceError: localVar is not defined</span></pre>
                    </div>
                </div>

                <div class="sub-section" id="block-scope">
                    <h3>3. 块级作用域（Block Scope）</h3>
                    <div class="explain">
                        ES6新增，用<strong>let/const</strong>在块（{}）内声明的变量，<strong>仅块内可访问</strong>（如if、for、while等块）。
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">if</span> (true) {
    <span class="code-keyword">let</span> blockVar = <span class="code-string">'I am block-scoped'</span>; <span class="code-comment">// 块内变量</span>
    <span class="code-keyword">const</span> blockConst = <span class="code-string">'I am also block-scoped'</span>;
    console.log(blockVar); <span class="code-comment">// 输出: I am block-scoped</span>
}

console.log(blockVar); <span class="code-comment">// 报错: ReferenceError: blockVar is not defined</span>
console.log(blockConst); <span class="code-comment">// 报错: ReferenceError: blockConst is not defined</span></pre>
                    </div>
                </div>
            </div>

            <!-- 闭包 section -->
            <div class="section" id="closure">
                <h2>四、闭包 - 函数与变量的"绑定"</h2>
                
                <div class="sub-section" id="closure-concept">
                    <h3>1. 概念理解</h3>
                    <div class="explain">
                        闭包是<strong>函数嵌套</strong>的特殊情况：
                        <ul>
                            <li>内部函数访问外部函数的变量（词法作用域）；</li>
                            <li>外部函数返回内部函数；</li>
                            <li>内部函数保持对外部变量的引用（不会被垃圾回收）。</li>
                        </ul>
                        简单说：<strong>闭包 = 函数 + 函数所处的词法环境</strong>。
                    </div>
                </div>

                <div class="sub-section" id="closure-example">
                    <h3>2. 实际应用 - 计数器</h3>
                    <div class="explain">
                        闭包常用于<strong>封装私有变量</strong>，防止变量被外部修改，比如实现一个计数器：
                    </div>
                    <div class="code-example">
                        <pre><span class="code-keyword">function</span> <span class="code-function">createCounter</span>() {
    <span class="code-keyword">let</span> count = 0; <span class="code-comment">// 私有变量（外部无法直接访问）</span>
    <span class="code-comment">// 返回内部函数（闭包）</span>
    <span class="code-keyword">return</span> <span class="code-keyword">function</span>() {
        count++; <span class="code-comment">// 访问外部函数的变量</span>
        <span class="code-keyword">return</span> count;
    };
}

<span class="code-comment">// 创建计数器实例</span>
<span class="code-keyword">const</span> counter = <span class="code-function">createCounter</span>();

console.log(<span class="code-function">counter</span>()); <span class="code-comment">// 输出: 1</span>
console.log(<span class="code-function">counter</span>()); <span class="code-comment">// 输出: 2</span>
console.log(<span class="code-function">counter</span>()); <span class="code-comment">// 输出: 3</span></pre>
                    </div>
                    <div class="explain">
                        说明：<code>count</code>是<code>createCounter</code>函数的局部变量，外部无法直接修改，但通过闭包（内部函数）可以间接访问和修改，实现了变量的私有性。
                    </div>
                </div>
            </div>

            <!-- 跳转到day3的链接 -->
            <a href="day3.html" class="next-day-link">前往Day3：数组与对象 →</a>
        </div>
    </div>

    <script>
        // 简单的导航激活状态切换
        document.querySelectorAll('.learning-item, .sub-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.learning-item').forEach(i => i.classList.remove('active'));
                
                // 如果点击的是子项，激活其父级
                if(this.classList.contains('sub-item')) {
                    this.parentNode.previousElementSibling.classList.add('active');
                } else {
                    this.classList.add('active');
                }
            });
        });
        
        // 自动滚动到顶部
        window.addEventListener('load', () => {
            if(window.location.hash) {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            }
        });
    </script>
</body>
</html>
