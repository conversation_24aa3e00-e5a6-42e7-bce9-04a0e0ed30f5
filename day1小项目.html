<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生成绩管理系统 - JavaScript实战项目</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(90deg, #3498db, #2c3e50);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .knowledge-summary {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 30px;
            background-color: #f8f9fa;
            border-bottom: 2px solid #eaeaea;
        }
        
        .knowledge-card {
            flex: 1;
            min-width: 250px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
        }
        
        .knowledge-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .knowledge-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .knowledge-card ul {
            padding-left: 20px;
        }
        
        .knowledge-card li {
            margin-bottom: 8px;
        }
        
        .content-wrapper {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
            gap: 30px;
        }
        
        .form-section {
            flex: 1;
            min-width: 300px;
            background: #fafafa;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .results-section {
            flex: 2;
            min-width: 300px;
            background: #fafafa;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(90deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            background: linear-gradient(90deg, #2980b9, #3498db);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .btn-reset {
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }
        
        .btn-reset:hover {
            background: linear-gradient(90deg, #c0392b, #e74c3c);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }
        
        .stat-value {
            font-size: 2.2rem;
            font-weight: bold;
            color: #3498db;
            margin: 10px 0;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #7f8c8d;
        }
        
        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }
        
        .students-table th {
            background: linear-gradient(90deg, #3498db, #2980b9);
            color: white;
            text-align: left;
            padding: 15px;
        }
        
        .students-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .students-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .students-table tr:hover {
            background-color: #f1f9ff;
        }
        
        .grade-A { background-color: #e8f6f3; color: #00695c; }
        .grade-B { background-color: #e3f2fd; color: #0d47a1; }
        .grade-C { background-color: #fff8e1; color: #f57f17; }
        .grade-F { background-color: #ffebee; color: #c62828; }
        
        .code-section {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 30px;
            margin-top: 30px;
        }
        
        .code-container {
            background: #1a2530;
            border-radius: 10px;
            padding: 20px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        
        .code-comment { color: #5dade2; }
        .code-keyword { color: #e74c3c; }
        .code-function { color: #3498db; }
        .code-string { color: #2ecc71; }
        .code-variable { color: #f39c12; }
        
        @media (max-width: 768px) {
            .content-wrapper {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>学生成绩管理系统</h1>
            <p class="subtitle">使用JavaScript变量声明、数据类型、运算符、条件语句和循环的综合实战项目</p>
        </header>
        
        <div class="knowledge-summary">
            <div class="knowledge-card">
                <h3>变量声明</h3>
                <ul>
                    <li>使用 <strong>let</strong> 声明可重新赋值的变量</li>
                    <li>使用 <strong>const</strong> 声明不能重新赋值的常量</li>
                    <li>避免使用 <strong>var</strong>（存在变量提升问题）</li>
                </ul>
            </div>
            
            <div class="knowledge-card">
                <h3>数据类型</h3>
                <ul>
                    <li>基本类型：String, Number, Boolean</li>
                    <li>引用类型：Object, Array, Function</li>
                    <li>特殊类型：null, undefined</li>
                </ul>
            </div>
            
            <div class="knowledge-card">
                <h3>运算符</h3>
                <ul>
                    <li>算术：+, -, *, /, %</li>
                    <li>比较：===, !==, >, &lt;, >=, &lt;=</li>
                    <li>逻辑：&&, ||, !</li>
                    <li>三元运算符：条件 ? 值1 : 值2</li>
                </ul>
            </div>
            
            <div class="knowledge-card">
                <h3>流程控制</h3>
                <ul>
                    <li>条件语句：if/else, switch</li>
                    <li>循环语句：for, while</li>
                    <li>跳出循环：break, continue</li>
                    <li>数组遍历：forEach, map</li>
                </ul>
            </div>
        </div>
        
        <div class="content-wrapper">
            <div class="form-section">
                <h2>添加学生成绩</h2>
                
                <div class="form-group">
                    <label for="studentName">学生姓名</label>
                    <input type="text" id="studentName" placeholder="输入学生姓名">
                </div>
                
                <div class="form-group">
                    <label for="studentId">学号</label>
                    <input type="text" id="studentId" placeholder="输入学号">
                </div>
                
                <div class="form-group">
                    <label for="course">课程</label>
                    <select id="course">
                        <option value="语文">语文</option>
                        <option value="数学">数学</option>
                        <option value="英语">英语</option>
                        <option value="物理">物理</option>
                        <option value="化学">化学</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="score">成绩 (0-100)</label>
                    <input type="number" id="score" min="0" max="100" placeholder="输入成绩">
                </div>
                
                <button class="btn" onclick="addStudent()">添加学生成绩</button>
                <button class="btn btn-reset" onclick="resetForm()">重置表单</button>
                <button class="btn" onclick="generateSampleData()" style="background: linear-gradient(90deg, #9b59b6, #8e44ad); margin-top: 15px;">
                    生成示例数据
                </button>
            </div>
            
            <div class="results-section">
                <h2>成绩统计分析</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalStudents">0</div>
                        <div class="stat-label">学生总数</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value" id="averageScore">0</div>
                        <div class="stat-label">平均成绩</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value" id="highestScore">0</div>
                        <div class="stat-label">最高分</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value" id="lowestScore">0</div>
                        <div class="stat-label">最低分</div>
                    </div>
                </div>
                
                <h3>学生成绩列表</h3>
                <div class="table-container">
                    <table class="students-table">
                        <thead>
                            <tr>
                                <th>学号</th>
                                <th>姓名</th>
                                <th>课程</th>
                                <th>成绩</th>
                                <th>等级</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="studentsTableBody">
                            <!-- 学生数据将动态添加到这里 -->
                        </tbody>
                    </table>
                </div>
                
                <button class="btn" onclick="calculateStatistics()" style="margin-top: 20px; background: linear-gradient(90deg, #27ae60, #2ecc71);">
                    重新计算统计数据
                </button>
            </div>
        </div>
        
        <div class="code-section">
            <h2 style="color: white; border-color: #3498db;">核心代码实现</h2>
            <div class="code-container">
                <pre><span class="code-comment">// 使用数组存储学生成绩数据</span>
<span class="code-keyword">const</span> <span class="code-variable">students</span> = [];

<span class="code-comment">// 添加学生成绩函数</span>
<span class="code-keyword">function</span> <span class="code-function">addStudent</span>() {
    <span class="code-keyword">const</span> <span class="code-variable">name</span> = document.<span class="code-function">getElementById</span>(<span class="code-string">'studentName'</span>).<span class="code-variable">value</span>;
    <span class="code-keyword">const</span> <span class="code-variable">id</span> = document.<span class="code-function">getElementById</span>(<span class="code-string">'studentId'</span>).<span class="code-variable">value</span>;
    <span class="code-keyword">const</span> <span class="code-variable">course</span> = document.<span class="code-function">getElementById</span>(<span class="code-string">'course'</span>).<span class="code-variable">value</span>;
    <span class="code-keyword">const</span> <span class="code-variable">score</span> = <span class="code-function">parseFloat</span>(document.<span class="code-function">getElementById</span>(<span class="code-string">'score'</span>).<span class="code-variable">value</span>);
    
    <span class="code-comment">// 输入验证（使用条件语句）</span>
    <span class="code-keyword">if</span> (!name || !id || !course || <span class="code-function">isNaN</span>(score)) {
        <span class="code-function">alert</span>(<span class="code-string">'请填写所有字段并确保成绩是有效数字！'</span>);
        <span class="code-keyword">return</span>;
    }
    
    <span class="code-keyword">if</span> (score &lt; <span class="code-number">0</span> || score > <span class="code-number">100</span>) {
        <span class="code-function">alert</span>(<span class="code-string">'成绩必须在0到100之间！'</span>);
        <span class="code-keyword">return</span>;
    }
    
    <span class="code-comment">// 创建学生对象（使用对象数据类型）</span>
    <span class="code-keyword">const</span> <span class="code-variable">student</span> = {
        id: id,
        name: name,
        course: course,
        score: score,
        grade: <span class="code-function">calculateGrade</span>(score)  <span class="code-comment">// 计算成绩等级</span>
    };
    
    <span class="code-comment">// 添加到学生数组</span>
    students.<span class="code-function">push</span>(student);
    
    <span class="code-comment">// 更新UI</span>
    <span class="code-function">updateStudentTable</span>();
    <span class="code-function">calculateStatistics</span>();
    
    <span class="code-comment">// 重置表单</span>
    <span class="code-function">resetForm</span>();
}

<span class="code-comment">// 计算成绩等级函数（使用条件语句）</span>
<span class="code-keyword">function</span> <span class="code-function">calculateGrade</span>(<span class="code-variable">score</span>) {
    <span class="code-keyword">if</span> (score >= <span class="code-number">90</span>) <span class="code-keyword">return</span> <span class="code-string">'A'</span>;
    <span class="code-keyword">if</span> (score >= <span class="code-number">80</span>) <span class="code-keyword">return</span> <span class="code-string">'B'</span>;
    <span class="code-keyword">if</span> (score >= <span class="code-number">60</span>) <span class="code-keyword">return</span> <span class="code-string">'C'</span>;
    <span class="code-keyword">return</span> <span class="code-string">'F'</span>;
}

<span class="code-comment">// 计算统计数据（使用循环和运算符）</span>
<span class="code-keyword">function</span> <span class="code-function">calculateStatistics</span>() {
    <span class="code-keyword">const</span> <span class="code-variable">totalStudents</span> = students.<span class="code-variable">length</span>;
    
    <span class="code-keyword">if</span> (totalStudents === <span class="code-number">0</span>) {
        <span class="code-function">updateStatsUI</span>(<span class="code-number">0</span>, <span class="code-number">0</span>, <span class="code-number">0</span>, <span class="code-number">0</span>);
        <span class="code-keyword">return</span>;
    }
    
    <span class="code-comment">// 使用循环计算总分、最高分、最低分</span>
    <span class="code-keyword">let</span> <span class="code-variable">totalScore</span> = <span class="code-number">0</span>;
    <span class="code-keyword">let</span> <span class="code-variable">highestScore</span> = students[<span class="code-number">0</span>].<span class="code-variable">score</span>;
    <span class="code-keyword">let</span> <span class="code-variable">lowestScore</span> = students[<span class="code-number">0</span>].<span class="code-variable">score</span>;
    
    <span class="code-comment">// 使用for循环遍历数组</span>
    <span class="code-keyword">for</span> (<span class="code-keyword">let</span> <span class="code-variable">i</span> = <span class="code-number">0</span>; i &lt; students.<span class="code-variable">length</span>; i++) {
        <span class="code-keyword">const</span> <span class="code-variable">studentScore</span> = students[i].<span class="code-variable">score</span>;
        <span class="code-variable">totalScore</span> += studentScore;  <span class="code-comment">// 使用算术运算符累加</span>
        
        <span class="code-comment">// 使用比较运算符判断最高分和最低分</span>
        <span class="code-keyword">if</span> (studentScore > highestScore) <span class="code-variable">highestScore</span> = studentScore;
        <span class="code-keyword">if</span> (studentScore &lt; lowestScore) <span class="code-variable">lowestScore</span> = studentScore;
    }
    
    <span class="code-keyword">const</span> <span class="code-variable">averageScore</span> = totalScore / totalStudents;
    
    <span class="code-comment">// 更新UI</span>
    <span class="code-function">updateStatsUI</span>(totalStudents, averageScore, highestScore, lowestScore);
}</pre>
            </div>
        </div>
    </div>

    <script>
        // 学生数据存储数组
        const students = [];
        
        // DOM元素引用
        const tableBody = document.getElementById('studentsTableBody');
        
        // 添加学生函数
        function addStudent() {
            // 获取表单值
            const name = document.getElementById('studentName').value;
            const id = document.getElementById('studentId').value;
            const course = document.getElementById('course').value;
            const score = parseFloat(document.getElementById('score').value);
            
            // 输入验证 - 使用条件语句
            if (!name || !id || !course || isNaN(score)) {
                alert('请填写所有字段并确保成绩是有效数字！');
                return;
            }
            
            if (score < 0 || score > 100) {
                alert('成绩必须在0到100之间！');
                return;
            }
            
            // 创建学生对象 - 使用对象数据类型
            const student = {
                id: id,
                name: name,
                course: course,
                score: score,
                grade: calculateGrade(score)  // 计算成绩等级
            };
            
            // 添加到学生数组
            students.push(student);
            
            // 更新UI
            updateStudentTable();
            calculateStatistics();
            
            // 重置表单
            resetForm();
        }
        
        // 计算成绩等级函数 - 使用条件语句
        function calculateGrade(score) {
            if (score >= 90) return 'A';
            if (score >= 80) return 'B';
            if (score >= 60) return 'C';
            return 'F';
        }
        
        // 更新学生表格
        function updateStudentTable() {
            // 清空表格
            tableBody.innerHTML = '';
            
            // 使用forEach循环遍历数组并创建表格行
            students.forEach((student, index) => {
                const row = document.createElement('tr');
                
                // 根据等级添加不同类名
                row.classList.add(`grade-${student.grade}`);
                
                // 创建单元格
                row.innerHTML = `
                    <td>${student.id}</td>
                    <td>${student.name}</td>
                    <td>${student.course}</td>
                    <td>${student.score}</td>
                    <td>${student.grade}</td>
                    <td>
                        <button onclick="removeStudent(${index})" style="padding: 5px 10px; background: #e74c3c; color: white; border: none; border-radius: 4px; cursor: pointer;">删除</button>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // 删除学生
        function removeStudent(index) {
            // 使用splice方法从数组中移除元素
            students.splice(index, 1);
            updateStudentTable();
            calculateStatistics();
        }
        
        // 计算统计数据
        function calculateStatistics() {
            const totalStudents = students.length;
            
            // 如果没有学生，重置统计
            if (totalStudents === 0) {
                updateStatsUI(0, 0, 0, 0);
                return;
            }
            
            // 使用循环计算总分、最高分、最低分
            let totalScore = 0;
            let highestScore = students[0].score;
            let lowestScore = students[0].score;
            
            // 使用for循环遍历数组
            for (let i = 0; i < students.length; i++) {
                const studentScore = students[i].score;
                totalScore += studentScore;  // 使用算术运算符累加
                
                // 使用比较运算符判断最高分和最低分
                if (studentScore > highestScore) highestScore = studentScore;
                if (studentScore < lowestScore) lowestScore = studentScore;
            }
            
            // 计算平均分
            const averageScore = totalScore / totalStudents;
            
            // 更新UI
            updateStatsUI(totalStudents, averageScore, highestScore, lowestScore);
        }
        
        // 更新统计数据UI
        function updateStatsUI(total, average, highest, lowest) {
            document.getElementById('totalStudents').textContent = total;
            document.getElementById('averageScore').textContent = average.toFixed(2);
            document.getElementById('highestScore').textContent = highest;
            document.getElementById('lowestScore').textContent = lowest;
        }
        
        // 重置表单
        function resetForm() {
            document.getElementById('studentName').value = '';
            document.getElementById('studentId').value = '';
            document.getElementById('course').value = '语文';
            document.getElementById('score').value = '';
            document.getElementById('studentName').focus();
        }
        
        // 生成示例数据
        function generateSampleData() {
            // 示例学生数据数组
            const sampleStudents = [
                {id: 'S001', name: '张三', course: '语文', score: 92},
                {id: 'S002', name: '李四', course: '数学', score: 88},
                {id: 'S003', name: '王五', course: '英语', score: 75},
                {id: 'S004', name: '赵六', course: '物理', score: 63},
                {id: 'S005', name: '钱七', course: '化学', score: 95}
            ];
            
            // 清空现有数据
            students.length = 0;
            
            // 添加示例数据
            sampleStudents.forEach(student => {
                students.push({
                    ...student,
                    grade: calculateGrade(student.score)
                });
            });
            
            // 更新UI
            updateStudentTable();
            calculateStatistics();
        }
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            generateSampleData();
        });
    </script>
</body>
</html>