<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>10天掌握JavaScript和Vue3学习计划</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .progress-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            font-weight: bold;
            color: #666;
        }

        .days-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .day-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid #667eea;
        }

        .day-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .day-card.completed {
            border-left-color: #4CAF50;
            background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
        }

        .day-card.current {
            border-left-color: #ff9800;
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        }

        .day-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .day-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
        }

        .day-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-pending {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-current {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-completed {
            background: #e8f5e9;
            color: #388e3c;
        }

        .day-time {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .topics {
            margin-bottom: 15px;
        }

        .topics h4 {
            color: #555;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .topic-list {
            list-style: none;
        }

        .topic-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
            color: #666;
            font-size: 0.9rem;
        }

        .topic-list li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #667eea;
            font-size: 0.8rem;
        }

        .project {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #667eea;
        }

        .project h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .project p {
            color: #666;
            font-size: 0.9rem;
        }

        .complete-btn {
            width: 100%;
            padding: 10px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin-top: 15px;
            transition: background 0.3s ease;
        }

        .complete-btn:hover {
            background: #5a6fd8;
        }

        .complete-btn:disabled {
            background: #4CAF50;
            cursor: not-allowed;
        }

        .resources-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .resource-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .resource-card h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .resource-link {
            display: block;
            color: #667eea;
            text-decoration: none;
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .resource-link:hover {
            text-decoration: underline;
        }

        .tips-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
            text-align: center;
        }

        .tips-section h3 {
            margin-bottom: 15px;
        }

        .tips-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .tip-item {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .days-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <div class="header">
            <h1>🚀 10天掌握JavaScript和Vue3</h1>
            <p>从零基础到前端开发入门的完整学习路径</p>
        </div>

        <!-- 进度条 -->
        <div class="progress-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>学习进度</h3>
                <button onclick="resetProgress()" style="padding: 8px 16px; background: #ff9800; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9rem; transition: background 0.3s ease;" onmouseover="this.style.background='#f57c00'" onmouseout="this.style.background='#ff9800'">🔄 重新开始学习</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">🚀 今天是第1天 - 开始你的前端学习之旅！</div>
        </div>

        <!-- 学习计划卡片 -->
        <div class="days-grid" id="daysGrid">
            <!-- Day 1 -->
            <div class="day-card current" data-day="1">
                <div class="day-header">
                    <div class="day-title">Day 1: JavaScript基础语法</div>
                    <div class="day-status status-current">进行中</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>变量声明 - let, const, var的区别</li>
                        <li>数据类型 - 基本类型和引用类型</li>
                        <li>运算符 - 算术、比较、逻辑运算符</li>
                        <li>条件语句 - if/else, switch</li>
                        <li>循环语句 - for, while, forEach</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>制作一个简单的计算器</p>
                </div>
                <a href="day1.html" style="display: block; text-align: center; padding: 10px; background: #4CAF50; color: white; text-decoration: none; border-radius: 8px; margin-top: 10px; transition: background 0.3s ease;" onmouseover="this.style.background='#45a049'" onmouseout="this.style.background='#4CAF50'">📚 开始学习 Day 1</a>
                <button class="complete-btn" onclick="completeDay(1)">完成第1天</button>
            </div>

            <!-- Day 2 -->
            <div class="day-card" data-day="2">
                <div class="day-header">
                    <div class="day-title">Day 2: 函数与作用域</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>函数定义 - 函数声明、函数表达式、箭头函数</li>
                        <li>参数传递 - 默认参数、剩余参数</li>
                        <li>作用域 - 全局作用域、函数作用域、块级作用域</li>
                        <li>闭包 - 理解闭包概念和应用</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>制作一个待办事项列表（纯JS版本）</p>
                </div>
                <a href="day2.html" style="display: block; text-align: center; padding: 10px; background: #4CAF50; color: white; text-decoration: none; border-radius: 8px; margin-top: 10px; transition: background 0.3s ease;" onmouseover="this.style.background='#45a049'" onmouseout="this.style.background='#4CAF50'">📚 开始学习 Day 2</a>
                <button class="complete-btn" onclick="completeDay(2)">完成第2天</button>
            </div>

            <!-- Day 3 -->
            <div class="day-card" data-day="3">
                <div class="day-header">
                    <div class="day-title">Day 3: 对象与数组</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>数组方法 - map, filter, reduce, find, forEach</li>
                        <li>对象操作 - 属性访问、方法定义、this关键字</li>
                        <li>解构赋值 - 数组解构、对象解构</li>
                        <li>展开运算符 - ...语法的使用</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>数据处理小工具（学生成绩管理）</p>
                </div>
                <button class="complete-btn" onclick="completeDay(3)">完成第3天</button>
            </div>

            <!-- Day 4 -->
            <div class="day-card" data-day="4">
                <div class="day-header">
                    <div class="day-title">Day 4: 异步编程</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>Promise - 创建和使用Promise</li>
                        <li>async/await - 现代异步编程语法</li>
                        <li>Fetch API - 网络请求基础</li>
                        <li>错误处理 - try/catch, Promise.catch()</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>天气查询应用（调用API）</p>
                </div>
                <button class="complete-btn" onclick="completeDay(4)">完成第4天</button>
            </div>

            <!-- Day 5 -->
            <div class="day-card" data-day="5">
                <div class="day-header">
                    <div class="day-title">Day 5: ES6+ 特性</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>模块化 - import/export</li>
                        <li>类 - class语法、继承</li>
                        <li>模板字符串 - 字符串插值</li>
                        <li>Symbol, Set, Map - 新的数据类型</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>模块化的图书管理系统</p>
                </div>
                <button class="complete-btn" onclick="completeDay(5)">完成第5天</button>
            </div>
            <!-- Day 6 -->
            <div class="day-card" data-day="6">
                <div class="day-header">
                    <div class="day-title">Day 6: Vue3 基础</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>Vue3 安装 - 使用Vite创建项目</li>
                        <li>模板语法 - 插值、指令（v-if, v-for, v-model）</li>
                        <li>响应式数据 - ref, reactive</li>
                        <li>事件处理 - @click, @input等</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>Vue版本的计算器</p>
                </div>
                <button class="complete-btn" onclick="completeDay(6)">完成第6天</button>
            </div>

            <!-- Day 7 -->
            <div class="day-card" data-day="7">
                <div class="day-header">
                    <div class="day-title">Day 7: 组件化开发</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>组件定义 - 单文件组件(.vue)</li>
                        <li>Props - 父子组件通信</li>
                        <li>Emit - 子组件向父组件传递事件</li>
                        <li>插槽 - slot的使用</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>可复用的卡片组件库</p>
                </div>
                <button class="complete-btn" onclick="completeDay(7)">完成第7天</button>
            </div>

            <!-- Day 8 -->
            <div class="day-card" data-day="8">
                <div class="day-header">
                    <div class="day-title">Day 8: Composition API</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>setup函数 - Vue3的核心特性</li>
                        <li>生命周期钩子 - onMounted, onUpdated等</li>
                        <li>计算属性 - computed</li>
                        <li>侦听器 - watch, watchEffect</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>购物车应用</p>
                </div>
                <button class="complete-btn" onclick="completeDay(8)">完成第8天</button>
            </div>

            <!-- Day 9 -->
            <div class="day-card" data-day="9">
                <div class="day-header">
                    <div class="day-title">Day 9: Vue Router + 状态管理</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：6-8小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>Vue Router - 路由配置、导航</li>
                        <li>Pinia - 状态管理基础</li>
                        <li>组件间通信 - provide/inject</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>多页面的博客应用</p>
                </div>
                <button class="complete-btn" onclick="completeDay(9)">完成第9天</button>
            </div>

            <!-- Day 10 -->
            <div class="day-card" data-day="10">
                <div class="day-header">
                    <div class="day-title">Day 10: 项目实战</div>
                    <div class="day-status status-pending">待开始</div>
                </div>
                <div class="day-time">⏰ 学习时间：8-10小时</div>
                <div class="topics">
                    <h4>📚 学习内容：</h4>
                    <ul class="topic-list">
                        <li>综合项目 - 整合所有学过的知识</li>
                        <li>代码优化 - 性能优化基础</li>
                        <li>部署上线 - 使用Netlify或Vercel</li>
                    </ul>
                </div>
                <div class="project">
                    <h4>🎯 实践项目：</h4>
                    <p>完整的任务管理应用</p>
                </div>
                <button class="complete-btn" onclick="completeDay(10)">完成第10天</button>
            </div>
        </div>

        <!-- 学习资源 -->
        <div class="resources-section">
            <h3>📖 学习资源推荐</h3>
            <div class="resources-grid">
                <div class="resource-card">
                    <h4>JavaScript 资源</h4>
                    <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript" class="resource-link" target="_blank">MDN JavaScript 文档</a>
                    <a href="https://zh.javascript.info/" class="resource-link" target="_blank">现代 JavaScript 教程</a>
                    <a href="https://es6.ruanyifeng.com/" class="resource-link" target="_blank">ES6 入门教程</a>
                    <a href="https://www.freecodecamp.org/" class="resource-link" target="_blank">FreeCodeCamp</a>
                </div>
                <div class="resource-card">
                    <h4>Vue3 资源</h4>
                    <a href="https://cn.vuejs.org/" class="resource-link" target="_blank">Vue3 官方文档</a>
                    <a href="https://router.vuejs.org/zh/" class="resource-link" target="_blank">Vue Router 文档</a>
                    <a href="https://pinia.vuejs.org/zh/" class="resource-link" target="_blank">Pinia 状态管理</a>
                    <a href="https://element-plus.org/zh-CN/" class="resource-link" target="_blank">Element Plus 组件库</a>
                </div>
                <div class="resource-card">
                    <h4>开发工具</h4>
                    <a href="https://code.visualstudio.com/" class="resource-link" target="_blank">VS Code 编辑器</a>
                    <a href="https://vitejs.dev/" class="resource-link" target="_blank">Vite 构建工具</a>
                    <a href="https://nodejs.org/" class="resource-link" target="_blank">Node.js 运行时</a>
                    <a href="https://git-scm.com/" class="resource-link" target="_blank">Git 版本控制</a>
                </div>
                <div class="resource-card">
                    <h4>在线练习</h4>
                    <a href="https://codepen.io/" class="resource-link" target="_blank">CodePen 在线编辑器</a>
                    <a href="https://codesandbox.io/" class="resource-link" target="_blank">CodeSandbox</a>
                    <a href="https://stackblitz.com/" class="resource-link" target="_blank">StackBlitz</a>
                    <a href="https://leetcode.cn/" class="resource-link" target="_blank">LeetCode 算法练习</a>
                </div>
            </div>
        </div>

        <!-- 学习技巧 -->
        <div class="tips-section">
            <h3>💡 高效学习技巧</h3>
            <div class="tips-list">
                <div class="tip-item">
                    <h4>🎯 代码优先</h4>
                    <p>每学一个概念立即写代码验证，不要只看不练</p>
                </div>
                <div class="tip-item">
                    <h4>🚀 项目驱动</h4>
                    <p>每天完成一个小项目，逐步增加项目复杂度</p>
                </div>
                <div class="tip-item">
                    <h4>📝 笔记整理</h4>
                    <p>记录重点概念和代码片段，建立自己的知识库</p>
                </div>
                <div class="tip-item">
                    <h4>🐛 错误调试</h4>
                    <p>遇到错误要深入理解原因，学会使用开发者工具</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 学习进度管理 - 重置为第一天开始
        let completedDays = [];
        let currentDay = 1;

        // 初始化页面
        function initializePage() {
            // 清除之前的进度，从第一天重新开始
            localStorage.removeItem('completedDays');
            localStorage.removeItem('currentDay');
            completedDays = [];
            currentDay = 1;

            updateProgress();
            updateDayCards();

            // 显示欢迎消息
            setTimeout(() => {
                alert('🎉 欢迎开始你的前端学习之旅！\n\n今天是第1天，我们将学习JavaScript基础语法。\n建议学习时间：6-8小时\n\n点击"开始学习 Day 1"按钮开始吧！');
            }, 1000);
        }

        // 完成某一天的学习
        function completeDay(day) {
            if (!completedDays.includes(day)) {
                completedDays.push(day);
                localStorage.setItem('completedDays', JSON.stringify(completedDays));
            }

            if (day === currentDay && currentDay < 10) {
                currentDay = day + 1;
                localStorage.setItem('currentDay', currentDay.toString());
            }

            updateProgress();
            updateDayCards();

            // 显示完成提示
            showCompletionMessage(day);
        }

        // 更新进度条
        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            const progress = (completedDays.length / 10) * 100;
            progressFill.style.width = progress + '%';

            if (completedDays.length === 10) {
                progressText.textContent = '🎉 恭喜！你已经完成了所有学习计划！';
            } else if (completedDays.length === 0) {
                progressText.textContent = `🚀 今天是第${currentDay}天 - 开始你的前端学习之旅！`;
            } else {
                progressText.textContent = `第${currentDay}天 - 已完成 ${completedDays.length}/10 天`;
            }
        }

        // 更新天数卡片状态
        function updateDayCards() {
            const dayCards = document.querySelectorAll('.day-card');

            dayCards.forEach(card => {
                const day = parseInt(card.dataset.day);
                const statusElement = card.querySelector('.day-status');
                const button = card.querySelector('.complete-btn');

                // 移除所有状态类
                card.classList.remove('completed', 'current');

                if (completedDays.includes(day)) {
                    // 已完成
                    card.classList.add('completed');
                    statusElement.textContent = '已完成';
                    statusElement.className = 'day-status status-completed';
                    button.textContent = '✅ 已完成';
                    button.disabled = true;
                } else if (day === currentDay) {
                    // 当前进行中
                    card.classList.add('current');
                    statusElement.textContent = '进行中';
                    statusElement.className = 'day-status status-current';
                    button.textContent = `完成第${day}天`;
                    button.disabled = false;
                } else {
                    // 待开始
                    statusElement.textContent = '待开始';
                    statusElement.className = 'day-status status-pending';
                    button.textContent = `开始第${day}天`;
                    button.disabled = day > currentDay;
                }
            });
        }

        // 显示完成提示
        function showCompletionMessage(day) {
            const messages = [
                '🎉 太棒了！JavaScript基础语法已掌握！',
                '🚀 函数和作用域学会了！继续加油！',
                '💪 对象和数组操作已精通！',
                '⚡ 异步编程不再是难题！',
                '🌟 ES6+特性已掌握！JavaScript基础完成！',
                '🎯 Vue3基础已入门！前端框架之旅开始！',
                '🧩 组件化开发思维已建立！',
                '🔥 Composition API已掌握！Vue3核心完成！',
                '🛣️ 路由和状态管理已学会！',
                '🏆 恭喜！你已经完成了全部学习计划！现在你可以开始实际项目开发了！'
            ];

            alert(messages[day - 1]);
        }

        // 重置进度
        function resetProgress() {
            if (confirm('🔄 确定要重新开始学习计划吗？\n\n这将清除所有学习进度，从第1天重新开始。')) {
                localStorage.removeItem('completedDays');
                localStorage.removeItem('currentDay');
                completedDays = [];
                currentDay = 1;
                updateProgress();
                updateDayCards();

                alert('✅ 学习进度已重置！\n\n现在可以从第1天重新开始你的前端学习之旅！');
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', initializePage);

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl + R 重置进度
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                resetProgress();
            }
        });
    </script>
</body>
</html>
