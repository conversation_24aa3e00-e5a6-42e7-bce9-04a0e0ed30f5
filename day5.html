<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ES6+核心特性深度解析</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Ubuntu', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2980, #26d0ce);
            color: #333;
            min-height: 100vh;
            padding: 0;
            background-attachment: fixed;
            display: flex;
        }
        
        /* 侧边导航栏 */
        .sidebar {
            width: 280px;
            background: rgba(13, 22, 55, 0.95);
            backdrop-filter: blur(15px);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            overflow-y: auto;
            padding: 20px 0;
            box-shadow: 5px 0 25px rgba(0, 0, 0, 0.3);
            z-index: 100;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-title {
            color: white;
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
        }
        
        .sidebar-title i {
            color: #3498db;
        }
        
        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .nav-menu {
            list-style: none;
            padding: 0 15px;
        }
        
        .nav-item {
            margin-bottom: 8px;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            background: rgba(52, 152, 219, 0.25);
        }
        
        .nav-item:hover {
            background: rgba(52, 152, 219, 0.2);
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 14px 20px;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .nav-link i {
            width: 24px;
            text-align: center;
            font-size: 1.2rem;
        }
        
        .nav-link:hover {
            color: white;
            transform: translateX(5px);
        }
        
        .nav-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 20px 0;
        }
        
        .sidebar-footer {
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
            text-align: center;
            margin-top: 20px;
        }
        
        .sidebar-footer a {
            color: #3498db;
            text-decoration: none;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 40px;
        }
        
        header {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px 40px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            color: white;
            font-size: 3.2rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.4rem;
            max-width: 850px;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .knowledge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(550px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .knowledge-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.18);
            transition: transform 0.4s ease, box-shadow 0.4s ease;
        }
        
        .knowledge-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
        }
        
        .card-header {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: white;
            padding: 22px;
            display: flex;
            align-items: center;
            gap: 18px;
        }
        
        .card-header i {
            font-size: 2.4rem;
        }
        
        .card-title {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .card-subtitle {
            font-weight: 400;
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 5px;
        }
        
        .card-body {
            padding: 28px;
        }
        
        .knowledge-points {
            margin-bottom: 25px;
        }
        
        .point-title {
            font-size: 1.3rem;
            color: #1a2980;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e6e6ff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .point-title i {
            color: #6a11cb;
        }
        
        .knowledge-list {
            padding-left: 30px;
            margin-bottom: 20px;
        }
        
        .knowledge-list li {
            margin-bottom: 12px;
            line-height: 1.7;
            position: relative;
        }
        
        .knowledge-list li:before {
            content: "•";
            color: #2575fc;
            font-weight: bold;
            display: inline-block; 
            width: 1em;
            margin-left: -1em;
        }
        
        .code-container {
            background: #1e293b;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
        }
        
        .code-header {
            background: #0f172a;
            color: #e2e8f0;
            padding: 14px 20px;
            font-size: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #334155;
        }
        
        .copy-btn {
            background: linear-gradient(to right, #4f46e5, #7c3aed);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.92rem;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .copy-btn:hover {
            background: linear-gradient(to right, #7c3aed, #a855f7);
            transform: translateY(-2px);
        }
        
        pre {
            padding: 22px;
            color: #e2e8f0;
            overflow-x: auto;
            line-height: 1.5;
            font-size: 1rem;
            margin: 0;
            tab-size: 2;
        }
        
        .code-keyword {
            color: #60a5fa;
        }
        
        .code-function {
            color: #fbbf24;
        }
        
        .code-string {
            color: #34d399;
        }
        
        .code-comment {
            color: #94a3b8;
            font-style: italic;
        }
        
        .code-operator {
            color: #f87171;
        }
        
        .output-container {
            background: #f0f5ff;
            border: 1px solid #c7d2fe;
            border-radius: 12px;
            padding: 18px;
            margin-top: 22px;
        }
        
        .output-title {
            color: #3730a3;
            font-weight: 700;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.2rem;
        }
        
        .output-content {
            background: white;
            padding: 18px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 1.05rem;
            line-height: 1.6;
            min-height: 100px;
            overflow-x: auto;
            white-space: pre-wrap;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .note-box {
            background: #e0f2fe;
            border-left: 4px solid #0ea5e9;
            padding: 18px;
            border-radius: 0 8px 8px 0;
            margin: 22px 0;
            font-size: 1.05rem;
            line-height: 1.7;
        }
        
        .note-title {
            font-weight: 700;
            color: #0c4a6e;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        footer {
            text-align: center;
            color: white;
            padding: 30px;
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 20px;
        }
        
        .footer-icons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            font-size: 1.8rem;
        }
        
        .footer-icons a {
            color: white;
            transition: all 0.3s;
        }
        
        .footer-icons a:hover {
            color: #fbbf24;
            transform: translateY(-3px);
        }
        
        .progress-container {
            position: fixed;
            top: 0;
            left: 280px;
            right: 0;
            height: 5px;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }
        
        .progress-bar {
            height: 5px;
            background: linear-gradient(90deg, #ff8a00, #da1b60);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 220px;
            }
            
            .main-content {
                margin-left: 220px;
            }
            
            .progress-container {
                left: 220px;
            }
        }
        
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .sidebar-toggle {
                display: block;
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 101;
                background: linear-gradient(135deg, #6a11cb, #2575fc);
                color: white;
                border: none;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                font-size: 1.5rem;
                cursor: pointer;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            }
            
            .main-content {
                margin-left: 0;
                padding: 80px 20px 20px;
            }
            
            .progress-container {
                left: 0;
            }
            
            .knowledge-grid {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.2rem;
            }
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2.2rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
            
            .sidebar-header {
                padding: 0 15px 15px;
            }
            
            .sidebar-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <button class="sidebar-toggle">
        <i class="fas fa-bars"></i>
    </button>

    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <div class="sidebar">
        <div class="sidebar-header">
            <h2 class="sidebar-title"><i class="fab fa-js"></i> ES6+特性</h2>
            <p class="sidebar-subtitle">现代JavaScript核心特性深度导航</p>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item active">
                <a href="#modules" class="nav-link">
                    <i class="fas fa-cubes"></i>
                    <span>模块化 (import/export)</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#class" class="nav-link">
                    <i class="fas fa-shapes"></i>
                    <span>类 (Class)</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#template-literals" class="nav-link">
                    <i class="fas fa-code"></i>
                    <span>模板字符串</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#data-types" class="nav-link">
                    <i class="fas fa-database"></i>
                    <span>新数据类型</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#destructuring" class="nav-link">
                    <i class="fas fa-project-diagram"></i>
                    <span>解构赋值</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#async" class="nav-link">
                    <i class="fas fa-sync-alt"></i>
                    <span>异步编程</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#generators" class="nav-link">
                    <i class="fas fa-infinity"></i>
                    <span>生成器</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#promises" class="nav-link">
                    <i class="fas fa-handshake"></i>
                    <span>Promise</span>
                </a>
            </li>
        </ul>
        
        <div class="nav-divider"></div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#resources" class="nav-link">
                    <i class="fas fa-book"></i>
                    <span>学习资源</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#examples" class="nav-link">
                    <i class="fas fa-laptop-code"></i>
                    <span>示例项目</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#tools" class="nav-link">
                    <i class="fas fa-tools"></i>
                    <span>开发工具</span>
                </a>
            </li>
        </ul>
        
        <div class="sidebar-footer">
            <p>© 2023 ES6+指南<br>持续更新中</p>
        </div>
    </div>
    
    <div class="main-content">
        <header>
            <h1>ES6+核心特性深度解析</h1>
            <p class="subtitle">掌握现代JavaScript开发必备技能 - 全面解析箭头函数、解构赋值、Promise、异步函数等新特性</p>
        </header>
        
        <div class="knowledge-grid">
            <!-- 模块化特性卡片 -->
            <div class="knowledge-card" id="modules">
                <div class="card-header">
                    <i class="fas fa-cubes"></i>
                    <div>
                        <h2 class="card-title">模块化 (import/export)</h2>
                        <p class="card-subtitle">代码组织与封装的现代化解决方案</p>
                    </div>
                </div>
                <div class="card-body">
                    <div class="knowledge-points">
                        <div class="point-title">
                            <i class="fas fa-brain"></i> 核心概念
                        </div>
                        <ul class="knowledge-list">
                            <li><strong>模块作用域</strong> - 每个模块拥有独立作用域，避免全局污染</li>
                            <li><strong>默认导出</strong> - 每个模块仅能有一个默认导出(export default)</li>
                            <li><strong>命名导出</strong> - 可导出多个具名变量(export const/function)</li>
                            <li><strong>静态分析</strong> - 在编译时确定导入导出关系</li>
                            <li><strong>动态导入</strong> - 使用import()函数实现按需加载</li>
                        </ul>
                        
                        <div class="point-title">
                            <i class="fas fa-lightbulb"></i> 应用场景
                        </div>
                        <ul class="knowledge-list">
                            <li>前端框架组件化开发(Vue/React组件)</li>
                            <li>Node.js服务端应用结构组织</li>
                            <li>第三方库封装与发布(NPM包)</li>
                            <li>代码分割与按需加载(优化性能)</li>
                        </ul>
                    </div>
                    
                    <div class="code-container">
                        <div class="code-header">
                            <span>mathUtils.js - 实用数学函数模块</span>
                            <button class="copy-btn"><i class="fas fa-copy"></i> 复制代码</button>
                        </div>
                        <pre><code><span class="code-comment">// 默认导出：计算阶乘函数</span>
<span class="code-keyword">export default function</span> <span class="code-function">factorial</span>(n) {
  <span class="code-keyword">if</span> (n <= 1) <span class="code-keyword">return</span> 1;
  <span class="code-keyword">return</span> n * <span class="code-function">factorial</span>(n - 1);
}

<span class="code-comment">// 命名导出：数学常量</span>
<span class="code-keyword">export const</span> PI = 3.141592653589793;
<span class="code-keyword">export const</span> E = 2.718281828459045;

<span class="code-comment">// 命名导出：数学函数</span>
<span class="code-keyword">export function</span> <span class="code-function">square</span>(x) {
  <span class="code-keyword">return</span> x * x;
}

<span class="code-keyword">export const</span> <span class="code-function">cube</span> = x => x * x * x;</code></pre>
                    </div>
                    
                    <div class="code-container">
                        <div class="code-header">
                            <span>app.js - 主应用模块</span>
                            <button class="copy-btn"><i class="fas fa-copy"></i> 复制代码</button>
                        </div>
                        <pre><code><span class="code-comment">// 导入默认导出（可重命名）</span>
<span class="code-keyword">import</span> calcFactorial <span class="code-keyword">from</span> <span class="code-string">'./mathUtils.js'</span>;

<span class="code-comment">// 导入命名导出（可选择导入）</span>
<span class="code-keyword">import</span> { PI, square, cube <span class="code-keyword">as</span> cubeFn } <span class="code-keyword">from</span> <span class="code-string">'./mathUtils.js'</span>;

<span class="code-comment">// 使用导入的函数和常量</span>
console.<span class="code-function">log</span>(<span class="code-string">"5的阶乘:"</span>, <span class="code-function">calcFactorial</span>(5)); <span class="code-comment">// 120</span>
console.<span class="code-function">log</span>(<span class="code-string">"圆周率:"</span>, PI); <span class="code-comment">// 3.141592653589793</span>
console.<span class="code-function">log</span>(<span class="code-string">"4的平方:"</span>, <span class="code-function">square</span>(4)); <span class="code-comment">// 16</span>
console.<span class="code-function">log</span>(<span class="code-string">"3的立方:"</span>, <span class="code-function">cubeFn</span>(3)); <span class="code-comment">// 27</span></code></pre>
                    </div>
                    
                    <div class="output-container">
                        <div class="output-title"><i class="fas fa-terminal"></i> 运行结果</div>
                        <div class="output-content">5的阶乘: 120
圆周率: 3.141592653589793
4的平方: 16
3的立方: 27</div>
                    </div>
                </div>
            </div>
            
            <!-- 类特性卡片 -->
            <div class="knowledge-card" id="class">
                <div class="card-header">
                    <i class="fas fa-shapes"></i>
                    <div>
                        <h2 class="card-title">类 (Class)</h2>
                        <p class="card-subtitle">面向对象编程的现代化实现</p>
                    </div>
                </div>
                <div class="card-body">
                    <div class="knowledge-points">
                        <div class="point-title">
                            <i class="fas fa-brain"></i> 核心概念
                        </div>
                        <ul class="knowledge-list">
                            <li><strong>构造函数</strong> - 创建对象时初始化实例属性</li>
                            <li><strong>实例方法</strong> - 绑定到原型对象供所有实例共享</li>
                            <li><strong>静态方法</strong> - 使用static定义，通过类名调用</li>
                            <li><strong>继承</strong> - 使用extends关键字实现类继承</li>
                            <li><strong>super</strong> - 访问父类的构造函数和方法</li>
                            <li><strong>私有字段</strong> - ES2022引入(#前缀)，真正私有化</li>
                            <li><strong>Getter/Setter</strong> - 访问控制和计算属性</li>
                        </ul>
                        
                        <div class="point-title">
                            <i class="fas fa-trophy"></i> 优势
                        </div>
                        <ul class="knowledge-list">
                            <li>更接近传统面向对象语言的语法</li>
                            <li>内置继承机制，避免原型链操作复杂性</li>
                            <li>更好的封装性和代码组织能力</li>
                            <li>与现代框架（React/Vue）组件模型紧密结合</li>
                        </ul>
                    </div>
                    
                    <div class="code-container">
                        <div class="code-header">
                            <span>Animal基类与Dog子类</span>
                            <button class="copy-btn"><i class="fas fa-copy"></i> 复制代码</button>
                        </div>
                        <pre><code><span class="code-keyword">class</span> <span class="code-function">Animal</span> {
  <span class="code-comment">// 私有字段（ES2022）</span>
  #age;
  
  <span class="code-comment">// 构造函数</span>
  <span class="code-function">constructor</span>(name, age) {
    <span class="code-keyword">this</span>.name = name;
    <span class="code-keyword">this</span>.#age = age;
  }
  
  <span class="code-comment">// Getter方法</span>
  <span class="code-keyword">get</span> <span class="code-function">age</span>() {
    <span class="code-keyword">return</span> <span class="code-keyword">this</span>.#age;
  }
  
  <span class="code-comment">// 实例方法</span>
  <span class="code-function">speak</span>() {
    <span class="code-keyword">return</span> <span class="code-string">`<span class="code-keyword">${</span><span class="code-keyword">this</span>.name<span class="code-keyword">}</span>发出声音！`</span>;
  }
  
  <span class="code-comment">// 静态方法</span>
  <span class="code-keyword">static</span> <span class="code-function">create</span>(name) {
    <span class="code-keyword">return</span> <span class="code-keyword">new</span> <span class="code-function">Animal</span>(name, 0);
  }
}

<span class="code-keyword">class</span> <span class="code-function">Dog</span> <span class="code-keyword">extends</span> Animal {
  <span class="code-function">constructor</span>(name, age, breed) {
    <span class="code-keyword">super</span>(name, age); <span class="code-comment">// 调用父类构造函数</span>
    <span class="code-keyword">this</span>.breed = breed;
  }
  
  <span class="code-comment">// 方法重写</span>
  <span class="code-function">speak</span>() {
    <span class="code-keyword">return</span> <span class="code-string">`<span class="code-keyword">${</span><span class="code-keyword">super</span>.speak()<span class="code-keyword">}</span> 汪汪！`</span>;
  }
  
  <span class="code-comment">// 新增方法</span>
  <span class="code-function">fetch</span>() {
    <span class="code-keyword">return</span> <span class="code-string">`<span class="code-keyword">${</span><span class="code-keyword">this</span>.name<span class="code-keyword">}</span>把球叼回来了！`</span>;
  }
}

<span class="code-comment">// 创建实例</span>
<span class="code-keyword">const</span> dog = <span class="code-keyword">new</span> <span class="code-function">Dog</span>(<span class="code-string">'旺财'</span>, 3, <span class="code-string">'金毛'</span>);
console.<span class="code-function">log</span>(dog.<span class="code-function">speak</span>()); 
console.<span class="code-function">log</span>(dog.<span class="code-function">fetch</span>());
console.<span class="code-function">log</span>(<span class="code-string">"年龄:"</span>, dog.age); <span class="code-comment">// 使用getter</span>

<span class="code-comment">// 使用静态方法</span>
<span class="code-keyword">const</span> puppy = Animal.<span class="code-function">create</span>(<span class="code-string">'小黄'</span>);
console.<span class="code-function">log</span>(puppy.<span class="code-function">speak</span>());</code></pre>
                    </div>
                    
                    <div class="output-container">
                        <div class="output-title"><i class="fas fa-terminal"></i> 运行结果</div>
                        <div class="output-content">旺财发出声音！ 汪汪！
旺财把球叼回来了！
年龄: 3
小黄发出声音！</div>
                    </div>
                </div>
            </div>
            
            <!-- 模板字符串特性卡片 -->
            <div class="knowledge-card" id="template-literals">
                <div class="card-header">
                    <i class="fas fa-code"></i>
                    <div>
                        <h2 class="card-title">模板字符串</h2>
                        <p class="code-string">增强的字符串处理能力</p>
                    </div>
                </div>
                <div class="card-body">
                    <div class="knowledge-points">
                        <div class="point-title">
                            <i class="fas fa-brain"></i> 核心特性
                        </div>
                        <ul class="knowledge-list">
                            <li><strong>多行字符串</strong> - 保留换行符和缩进</li>
                            <li><strong>表达式插值</strong> - 使用${}嵌入任意表达式</li>
                            <li><strong>标签模板</strong> - 自定义模板处理函数</li>
                            <li><strong>原始字符串</strong> - 使用String.raw访问原始内容</li>
                            <li><strong>嵌套模板</strong> - 在${}中嵌套使用模板字符串</li>
                        </ul>
                        
                        <div class="point-title">
                            <i class="fas fa-wand-magic-sparkles"></i> 优势
                        </div>
                        <ul class="knowledge-list">
                            <li>简化复杂字符串拼接</li>
                            <li>更直观的HTML模板生成</li>
                            <li>支持多行文本无需转义</li>
                            <li>通过标签模板实现高级功能</li>
                            <li>提高代码可读性和可维护性</li>
                        </ul>
                    </div>
                    
                    <div class="code-container">
                        <div class="code-header">
                            <span>模板字符串高级用法</span>
                            <button class="copy-btn"><i class="fas fa-copy"></i> 复制代码</button>
                        </div>
                        <pre><code><span class="code-comment">// 1. 基本用法</span>
<span class="code-keyword">const</span> product = <span class="code-string">'笔记本电脑'</span>;
<span class="code-keyword">const</span> price = 8999;
<span class="code-keyword">const</span> discount = 0.85;
<span class="code-keyword">const</span> message = <span class="code-string">`商品: <span class="code-keyword">${</span>product<span class="code-keyword">}</span>
原价: <span class="code-keyword">${</span>price<span class="code-keyword">}</span>元
折扣: <span class="code-keyword">${</span>discount * 100<span class="code-keyword">}</span>%
实付: <span class="code-keyword">${</span>(price * discount).<span class="code-function">toFixed</span>(2)<span class="code-keyword">}</span>元`</span>;
console.<span class="code-function">log</span>(message);

<span class="code-comment">// 2. 生成HTML模板</span>
<span class="code-keyword">const</span> books = [
  { title: <span class="code-string">'JavaScript高级程序设计'</span>, author: <span class="code-string">'Nicholas C. Zakas'</span>, price: 128 },
  { title: <span class="code-string">'深入理解ES6'</span>, author: <span class="code-string">'Nicholas C. Zakas'</span>, price: 79 },
  { title: <span class="code-string">'你不知道的JavaScript'</span>, author: <span class="code-string">'Kyle Simpson'</span>, price: 108 }
];

<span class="code-keyword">const</span> bookListHTML = <span class="code-string">`
&lt;div class="book-list"&gt;
  &lt;h2&gt;推荐书籍&lt;/h2&gt;
  <span class="code-keyword">${</span>books.<span class="code-function">map</span>(book => <span class="code-string">`
    &lt;div class="book-item"&gt;
      &lt;h3&gt;<span class="code-keyword">${</span>book.title<span class="code-keyword">}</span>&lt;/h3&gt;
      &lt;p class="author"&gt;作者: <span class="code-keyword">${</span>book.author<span class="code-keyword">}</span>&lt;/p&gt;
      &lt;p class="price"&gt;价格: ¥<span class="code-keyword">${</span>book.price.<span class="code-function">toFixed</span>(2)<span class="code-keyword">}</span>&lt;/p&gt;
    &lt;/div&gt;
  `</span>).<span class="code-function">join</span>(<span class="code-string">''</span>)<span class="code-keyword">}</span>
&lt;/div&gt;
`</span>;
console.<span class="code-function">log</span>(bookListHTML);</code></pre>
                    </div>
                    
                    <div class="output-container">
                        <div class="output-title"><i class="fas fa-terminal"></i> 运行结果</div>
                        <div class="output-content">商品: 笔记本电脑
原价: 8999元
折扣: 85%
实付: 7649.15元

&lt;div class="book-list"&gt;
  &lt;h2&gt;推荐书籍&lt;/h2&gt;
    &lt;div class="book-item"&gt;
      &lt;h3&gt;JavaScript高级程序设计&lt;/h3&gt;
      &lt;p class="author"&gt;作者: Nicholas C. Zakas&lt;/p&gt;
      &lt;p class="price"&gt;价格: ¥128.00&lt;/p&gt;
    &lt;/div&gt;
    &lt;div class="book-item"&gt;
      &lt;h3&gt;深入理解ES6&lt;/h3&gt;
      &lt;p class="author"&gt;作者: Nicholas C. Zakas&lt;/p&gt;
      &lt;p class="price"&gt;价格: ¥79.00&lt;/p&gt;
    &lt;/div&gt;
    &lt;div class="book-item"&gt;
      &lt;h3&gt;你不知道的JavaScript&lt;/h3&gt;
      &lt;p class="author"&gt;作者: Kyle Simpson&lt;/p&gt;
      &lt;p class="price"&gt;价格: ¥108.00&lt;/p&gt;
    &lt;/div&gt;
&lt;/div&gt;</div>
                    </div>
                </div>
            </div>
            
            <!-- 新数据类型特性卡片 -->
            <div class="knowledge-card" id="data-types">
                <div class="card-header">
                    <i class="fas fa-database"></i>
                    <div>
                        <h2 class="card-title">新数据类型 (Symbol, Set, Map)</h2>
                        <p class="card-subtitle">增强的数据结构能力</p>
                    </div>
                </div>
                <div class="card-body">
                    <div class="knowledge-points">
                        <div class="point-title">
                            <i class="fas fa-brain"></i> 核心类型
                        </div>
                        <ul class="knowledge-list">
                            <li><strong>Symbol</strong> - 唯一且不可变的值，用于对象属性键</li>
                            <li><strong>Set</strong> - 唯一值集合，自动去重，快速存在检查</li>
                            <li><strong>Map</strong> - 键值对集合，键可以是任意类型</li>
                            <li><strong>WeakMap/WeakSet</strong> - 弱引用版本，避免内存泄漏</li>
                        </ul>
                        
                        <div class="point-title">
                            <i class="fas fa-lightbulb"></i> 应用场景
                        </div>
                        <ul class="knowledge-list">
                            <li>Symbol：创建私有属性、内置符号、避免命名冲突</li>
                            <li>Set：数组去重、交集/并集运算、唯一值存储</li>
                            <li>Map：键值对存储、对象元数据、缓存实现</li>
                            <li>WeakMap：私有数据存储、DOM节点关联数据</li>
                        </ul>
                    </div>
                    
                    <div class="code-container">
                        <div class="code-header">
                            <span>Set和Map高级应用</span>
                            <button class="copy-btn"><i class="fas fa-copy"></i> 复制代码</button>
                        </div>
                        <pre><code><span class="code-comment">// 1. Set操作 - 数组去重与集合运算</span>
<span class="code-keyword">const</span> setA = <span class="code-keyword">new</span> <span class="code-function">Set</span>([1, 2, 3, 4]);
<span class="code-keyword">const</span> setB = <span class="code-keyword">new</span> <span class="code-function">Set</span>([3, 4, 5, 6]);

<span class="code-comment">// 并集</span>
<span class="code-keyword">const</span> union = <span class="code-keyword">new</span> <span class="code-function">Set</span>([...setA, ...setB]);

<span class="code-comment">// 交集</span>
<span class="code-keyword">const</span> intersection = <span class="code-keyword">new</span> <span class="code-function">Set</span>(
  [...setA].<span class="code-function">filter</span>(x => setB.<span class="code-function">has</span>(x))
);

<span class="code-comment">// 差集 (A - B)</span>
<span class="code-keyword">const</span> difference = <span class="code-keyword">new</span> <span class="code-function">Set</span>(
  [...setA].<span class="code-function">filter</span>(x => !setB.<span class="code-function">has</span>(x))
);

console.<span class="code-function">log</span>(<span class="code-string">'并集:'</span>, [...union]); 
console.<span class="code-function">log</span>(<span class="code-string">'交集:'</span>, [...intersection]); 
console.<span class="code-function">log</span>(<span class="code-string">'差集:'</span>, [...difference]);

<span class="code-comment">// 2. Map实现缓存机制</span>
<span class="code-keyword">function</span> <span class="code-function">createCache</span>() {
  <span class="code-keyword">const</span> cache = <span class="code-keyword">new</span> <span class="code-function">Map</span>();
  
  <span class="code-keyword">return</span> {
    <span class="code-function">get</span>(key) {
      <span class="code-keyword">return</span> cache.<span class="code-function">get</span>(key);
    },
    <span class="code-function">set</span>(key, value, ttl = 3000) {
      <span class="code-keyword">const</span> timer = setTimeout(() => {
        cache.<span class="code-function">delete</span>(key);
      }, ttl);
      
      cache.<span class="code-function">set</span>(key, { value, timer });
    }
  };
}

<span class="code-comment">// 使用缓存</span>
<span class="code-keyword">const</span> cache = <span class="code-function">createCache</span>();
cache.<span class="code-function">set</span>(<span class="code-string">'user_123'</span>, { name: <span class="code-string">'张三'</span>, age: 30 });
console.<span class="code-function">log</span>(cache.<span class="code-function">get</span>(<span class="code-string">'user_123'</span>)); 
<span class="code-comment">// 3秒后自动删除</span></code></pre>
                    </div>
                    
                    <div class="output-container">
                        <div class="output-title"><i class="fas fa-terminal"></i> 运行结果</div>
                        <div class="output-content">并集: [1, 2, 3, 4, 5, 6]
交集: [3, 4]
差集: [1, 2]

{ value: { name: "张三", age: 30 }, timer: 2 }</div>
                    </div>
                    
                    <div class="note-box">
                        <div class="note-title"><i class="fas fa-star"></i> 最佳实践</div>
                        <p>当需要存储键值对且键类型不确定时优先使用Map而非普通对象。使用Set进行集合运算比数组更高效。WeakMap常用于存储与对象相关联的私有数据，当对象被垃圾回收时，WeakMap中的对应数据也会被自动清除。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>ES6+是现代JavaScript开发的基石 | 掌握这些特性将大幅提升您的开发效率和代码质量</p>
            <p>© 2023 ES6+核心特性深度解析 | 知识总结与代码示例</p>
            <div class="footer-icons">
                <a href="#"><i class="fab fa-github"></i></a>
                <a href="#"><i class="fab fa-codepen"></i></a>
                <a href="#"><i class="fab fa-js"></i></a>
                <a href="#"><i class="fas fa-book"></i></a>
            </div>
        </footer>
    </div>

    <script>
        // 添加复制功能
        document.querySelectorAll('.copy-btn').forEach(button => {
            button.addEventListener('click', function() {
                const codeBlock = this.parentElement.nextElementSibling;
                const textToCopy = codeBlock.textContent;
                
                navigator.clipboard.writeText(textToCopy).then(() => {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i> 已复制!';
                    
                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);
                }).catch(err => {
                    console.error('复制失败:', err);
                    this.innerHTML = '<i class="fas fa-times"></i> 失败!';
                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);
                });
            });
        });
        
        // 滚动进度条
        window.addEventListener('scroll', () => {
            const scrollTop = document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight;
            const clientHeight = document.documentElement.clientHeight;
            const scrollPercent = (scrollTop / (scrollHeight - clientHeight)) * 100;
            document.getElementById('progressBar').style.width = scrollPercent + '%';
        });
        
        // 移动端侧边栏切换
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if(sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('active');
            });
        }
        
        // 导航菜单激活状态
        const navItems = document.querySelectorAll('.nav-item');
        const sections = document.querySelectorAll('.knowledge-card');
        
        window.addEventListener('scroll', () => {
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                
                if(pageYOffset >= (sectionTop - sectionHeight / 3)) {
                    current = section.getAttribute('id');
                }
            });
            
            navItems.forEach(item => {
                item.classList.remove('active');
                const href = item.querySelector('a').getAttribute('href').substring(1);
                if(href === current) {
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>

