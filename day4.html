<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript异步编程详解</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2980, #26d0ce);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding-bottom: 50px;
        }
        
        header {
            text-align: center;
            margin-bottom: 40px;
            padding: 50px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.18);
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff9a9e, #fad0c4, #a1c4fd);
            z-index: 1;
        }
        
        h1 {
            font-size: 3.2rem;
            color: white;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }
        
        .subtitle {
            font-size: 1.3rem;
            color: #ffd700;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
            font-weight: 300;
            position: relative;
            z-index: 2;
        }
        
        .knowledge-section {
            background: rgba(255, 255, 255, 0.97);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            padding: 35px;
            margin-bottom: 30px;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .knowledge-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }
        
        .knowledge-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #6a11cb, #2575fc);
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e1e1e1;
        }
        
        .section-title i {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-size: 1.4rem;
        }
        
        h2 {
            font-size: 2rem;
            color: #2c3e50;
            background: linear-gradient(90deg, #2c3e50, #1a2980);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 5px;
        }
        
        h3 {
            font-size: 1.5rem;
            color: #1a2980;
            margin: 25px 0 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eaeaea;
            position: relative;
        }
        
        h3::after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, #6a11cb, #2575fc);
            border-radius: 3px;
        }
        
        .explanation {
            background-color: #f8faff;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #2575fc;
            font-size: 1.05rem;
        }
        
        .code-container {
            position: relative;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .code-header {
            background: #2c3e50;
            color: #fff;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
        }
        
        .code-header button {
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 0.9rem;
        }
        
        .code-header button:hover {
            background: rgba(255, 255, 255, 0.25);
        }
        
        .code-block {
            background: #1e293b;
            color: #f0f8ff;
            padding: 20px;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 1rem;
            overflow-x: auto;
            line-height: 1.5;
            tab-size: 4;
        }
        
        .comment {
            color: #94a3b8;
        }
        
        .keyword {
            color: #60a5fa;
        }
        
        .function {
            color: #c084fc;
        }
        
        .string {
            color: #34d399;
        }
        
        .number {
            color: #fbbf24;
        }
        
        .result-area {
            background: #eef5ff;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #2563eb;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(90deg, #2563eb, #1d4ed8);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 15px;
            font-size: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 14px rgba(0, 0, 0, 0.2);
        }
        
        .btn i {
            margin-right: 10px;
        }
        
        .notes {
            background: #f0f9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #0284c7;
        }
        
        .notes h4 {
            color: #0369a1;
            margin-bottom: 10px;
        }
        
        .notes ul {
            padding-left: 20px;
        }
        
        .notes li {
            margin-bottom: 8px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            color: white;
            padding: 30px;
            font-size: 1.1rem;
        }
        
        .footer a {
            color: #ffd700;
            text-decoration: none;
            font-weight: 600;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            header {
                padding: 30px 15px;
            }
            
            h1 {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
            
            .knowledge-section {
                padding: 25px 20px;
            }
            
            .section-title i {
                width: 42px;
                height: 42px;
                font-size: 1.2rem;
            }
            
            h2 {
                font-size: 1.7rem;
            }
            
            h3 {
                font-size: 1.35rem;
            }
            
            .code-block {
                font-size: 0.9rem;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-bolt"></i> JavaScript异步编程详解</h1>
            <p class="subtitle">深入理解Promise、async/await、Fetch API等核心概念及实际应用</p>
        </header>
        
        <!-- Promise详解 -->
        <div class="knowledge-section">
            <div class="section-title">
                <i class="fas fa-handshake"></i>
                <div>
                    <h2>Promise详解</h2>
                    <p>异步编程的核心解决方案</p>
                </div>
            </div>
            
            <div class="explanation">
                <p><strong>Promise</strong> 是JavaScript中处理异步操作的核心对象，代表一个尚未完成但预计会完成的操作。它有三种状态：</p>
                <ul>
                    <li><strong>pending</strong>（进行中）: 初始状态，操作尚未完成</li>
                    <li><strong>fulfilled</strong>（已成功）: 操作成功完成</li>
                    <li><strong>rejected</strong>（已失败）: 操作失败</li>
                </ul>
                <p>Promise解决了传统回调函数的"回调地狱"问题，使异步代码更易于编写和维护。</p>
            </div>
            
            <h3>创建Promise实例</h3>
            <div class="code-container">
                <div class="code-header">
                    <span>Promise基本创建</span>
                    <button id="runPromiseBasic">运行代码</button>
                </div>
                <pre class="code-block"><span class="keyword">const</span> promiseExample = <span class="keyword">new</span> <span class="function">Promise</span>((resolve, reject) => {
  <span class="comment">// 模拟异步操作（如API请求）</span>
  <span class="function">setTimeout</span>(() => {
    <span class="keyword">const</span> success = Math.<span class="function">random</span>() > <span class="number">0.3</span>;
    
    <span class="keyword">if</span> (success) {
      <span class="function">resolve</span>(<span class="string">"操作成功！数据已接收"</span>);
    } <span class="keyword">else</span> {
      <span class="function">reject</span>(<span class="keyword">new</span> <span class="function">Error</span>(<span class="string">"请求失败：服务器错误"</span>));
    }
  }, <span class="number">1500</span>);
});

<span class="comment">// 使用Promise</span>
promiseExample
  .<span class="function">then</span>(result => {
    console.<span class="function">log</span>(<span class="string">"成功："</span>, result);
  })
  .<span class="function">catch</span>(error => {
    console.<span class="function">error</span>(<span class="string">"失败："</span>, error.message);
  })
  .<span class="function">finally</span>(() => {
    console.<span class="function">log</span>(<span class="string">"操作完成（无论成功或失败）"</span>);
  });</pre>
            </div>
            
            <div class="result-area" id="promiseBasicResult">
                <p>点击"运行代码"查看结果...</p>
            </div>
            
            <h3>Promise链式调用</h3>
            <div class="code-container">
                <div class="code-header">
                    <span>链式调用示例</span>
                    <button id="runPromiseChain">运行代码</button>
                </div>
                <pre class="code-block"><span class="comment">// 模拟异步获取用户数据</span>
<span class="keyword">const</span> getUser = userId => {
  <span class="keyword">return</span> <span class="keyword">new</span> <span class="function">Promise</span>(resolve => {
    <span class="function">setTimeout</span>(() => {
      <span class="function">resolve</span>({ 
        id: userId, 
        name: <span class="string">`用户${userId}`</span>, 
        role: <span class="string">'member'</span> 
      });
    }, <span class="number">800</span>);
  });
};

<span class="comment">// 模拟获取用户权限</span>
<span class="keyword">const</span> getPermissions = user => {
  <span class="keyword">return</span> <span class="keyword">new</span> <span class="function">Promise</span>(resolve => {
    <span class="function">setTimeout</span>(() => {
      <span class="keyword">const</span> permissions = user.role === <span class="string">'admin'</span> 
        ? [<span class="string">'read'</span>, <span class="string">'write'</span>, <span class="string">'delete'</span>] 
        : [<span class="string">'read'</span>];
      <span class="function">resolve</span>({ ...user, permissions });
    }, <span class="number">600</span>);
  });
};

<span class="comment">// 链式调用</span>
getUser(<span class="number">101</span>)
  .<span class="function">then</span>(getPermissions)
  .<span class="function">then</span>(userWithPermissions => {
    console.<span class="function">log</span>(<span class="string">"用户信息："</span>, userWithPermissions);
  })
  .<span class="function">catch</span>(error => {
    console.<span class="function">error</span>(<span class="string">"出错："</span>, error);
  });</pre>
            </div>
            
            <div class="result-area" id="promiseChainResult">
                <p>点击"运行代码"查看结果...</p>
            </div>
            
            <div class="notes">
                <h4>Promise关键点：</h4>
                <ul>
                    <li>Promise构造器接收一个执行器函数(resolve, reject) => {}</li>
                    <li>只能从pending状态变为fulfilled或rejected（不可逆）</li>
                    <li>.then()方法接收两个可选参数（成功回调、失败回调）</li>
                    <li>.catch()相当于.then(null, rejectionCallback)</li>
                    <li>Promise链中返回的值会被自动包装为Promise</li>
                    <li>Promise.all()和Promise.race()用于处理多个Promise</li>
                </ul>
            </div>
        </div>
        
        <!-- async/await详解 -->
        <div class="knowledge-section">
            <div class="section-title">
                <i class="fas fa-sync-alt"></i>
                <div>
                    <h2>async/await详解</h2>
                    <p>异步编程的现代化解决方案</p>
                </div>
            </div>
            
            <div class="explanation">
                <p><strong>async/await</strong> 是ES8（2017）引入的语法糖，基于Promise但提供了更简洁的语法。</p>
                <ul>
                    <li><strong>async</strong>：声明一个异步函数，该函数总是返回一个Promise</li>
                    <li><strong>await</strong>：只能在async函数内部使用，用于等待Promise解析</li>
                </ul>
                <p>async/await使异步代码看起来像同步代码，大大提高了代码可读性和可维护性。</p>
            </div>
            
            <h3>基本使用</h3>
            <div class="code-container">
                <div class="code-header">
                    <span>async/await基础</span>
                    <button id="runAsyncBasic">运行代码</button>
                </div>
                <pre class="code-block"><span class="comment">// 模拟异步获取数据</span>
<span class="keyword">const</span> fetchData = <span class="function">async</span> () => {
  <span class="keyword">return</span> <span class="keyword">new</span> <span class="function">Promise</span>(resolve => {
    <span class="function">setTimeout</span>(() => {
      <span class="function">resolve</span>(<span class="string">"数据获取成功！"</span>);
    }, <span class="number">1000</span>);
  });
};

<span class="comment">// 使用async/await</span>
<span class="keyword">const</span> getData = <span class="function">async</span> () => {
  <span class="keyword">try</span> {
    console.<span class="function">log</span>(<span class="string">"开始获取数据..."</span>);
    <span class="keyword">const</span> data = <span class="keyword">await</span> <span class="function">fetchData</span>();
    console.<span class="function">log</span>(<span class="string">"数据："</span>, data);
    <span class="keyword">return</span> data;
  } <span class="keyword">catch</span> (error) {
    console.<span class="function">error</span>(<span class="string">"出错："</span>, error.message);
    <span class="keyword">throw</span> error;
  } <span class="keyword">finally</span> {
    console.<span class="function">log</span>(<span class="string">"数据获取过程结束"</span>);
  }
};

<span class="comment">// 调用async函数</span>
<span class="function">getData</span>();</pre>
            </div>
            
            <div class="result-area" id="asyncBasicResult">
                <p>点击"运行代码"查看结果...</p>
            </div>
            
            <h3>多步异步操作</h3>
            <div class="code-container">
                <div class="code-header">
                    <span>顺序执行异步操作</span>
                    <button id="runAsyncSequence">运行代码</button>
                </div>
                <pre class="code-block"><span class="comment">// 模拟异步API调用</span>
<span class="keyword">const</span> login = <span class="function">async</span> (email, password) => {
  <span class="keyword">return</span> <span class="keyword">new</span> <span class="function">Promise</span>(resolve => {
    <span class="function">setTimeout</span>(() => {
      <span class="function">resolve</span>({ token: <span class="string">`token_${<span class="function">Math</span>.random().<span class="function">toString</span>(36).<span class="function">substr</span>(2,9)}`</span> });
    }, <span class="number">800</span>);
  });
};

<span class="keyword">const</span> getUserProfile = <span class="function">async</span> token => {
  <span class="keyword">return</span> <span class="keyword">new</span> <span class="function">Promise</span>(resolve => {
    <span class="function">setTimeout</span>(() => {
      <span class="function">resolve</span>({
        name: <span class="string">"张三"</span>,
        email: <span class="string">"<EMAIL>"</span>,
        role: <span class="string">"admin"</span>
      });
    }, <span class="number">600</span>);
  });
};

<span class="comment">// 顺序执行多个异步操作</span>
<span class="keyword">const</span> loadUserData = <span class="function">async</span> () => {
  <span class="keyword">try</span> {
    <span class="keyword">const</span> loginData = <span class="keyword">await</span> <span class="function">login</span>(<span class="string">"<EMAIL>"</span>, <span class="string">"password123"</span>);
    console.<span class="function">log</span>(<span class="string">"登录成功，Token："</span>, loginData.token);
    
    <span class="keyword">const</span> userProfile = <span class="keyword">await</span> <span class="function">getUserProfile</span>(loginData.token);
    console.<span class="function">log</span>(<span class="string">"用户资料："</span>, userProfile);
    
    <span class="keyword">return</span> userProfile;
  } <span class="keyword">catch</span> (error) {
    console.<span class="function">error</span>(<span class="string">"加载失败："</span>, error);
  }
};

<span class="function">loadUserData</span>();</pre>
            </div>
            
            <div class="result-area" id="asyncSequenceResult">
                <p>点击"运行代码"查看结果...</p>
            </div>
            
            <div class="notes">
                <h4>async/await最佳实践：</h4>
                <ul>
                    <li>总是使用try/catch处理async函数中的错误</li>
                    <li>避免在循环中直接使用await（考虑使用Promise.all）</li>
                    <li>async函数返回值会被自动包装为Promise</li>
                    <li>await不仅可以等待Promise，也可以等待任何值</li>
                    <li>顶层await在ES2022中可用于模块</li>
                </ul>
            </div>
        </div>
        
        <!-- Fetch API详解 -->
        <div class="knowledge-section">
            <div class="section-title">
                <i class="fas fa-globe"></i>
                <div>
                    <h2>Fetch API详解</h2>
                    <p>现代浏览器内置的HTTP请求API</p>
                </div>
            </div>
            
            <div class="explanation">
                <p><strong>Fetch API</strong> 提供了简单、强大的网络请求能力，替代了传统的XMLHttpRequest。</p>
                <p>主要特点：</p>
                <ul>
                    <li>基于Promise设计，支持async/await</li>
                    <li>更简洁、灵活的API</li>
                    <li>内置请求和响应对象</li>
                    <li>支持流式数据处理</li>
                </ul>
            </div>
            
            <h3>基本GET请求</h3>
            <div class="code-container">
                <div class="code-header">
                    <span>Fetch GET示例</span>
                    <button id="runFetchGet">运行代码</button>
                </div>
                <pre class="code-block"><span class="comment">// 获取用户数据</span>
<span class="keyword">const</span> fetchUserData = <span class="function">async</span> (userId) => {
  <span class="keyword">try</span> {
    <span class="comment">// 发送GET请求</span>
    <span class="keyword">const</span> response = <span class="keyword">await</span> <span class="function">fetch</span>(<span class="string">`https://jsonplaceholder.typicode.com/users/${userId}`</span>);
    
    <span class="comment">// 检查响应状态（200-299表示成功）</span>
    <span class="keyword">if</span> (!response.ok) {
      <span class="keyword">throw</span> <span class="keyword">new</span> <span class="function">Error</span>(<span class="string">`请求失败，状态码: ${response.status}`</span>);
    }
    
    <span class="comment">// 解析JSON数据</span>
    <span class="keyword">const</span> userData = <span class="keyword">await</span> response.<span class="function">json</span>();
    
    <span class="keyword">return</span> userData;
  } <span class="keyword">catch</span> (error) {
    console.<span class="function">error</span>(<span class="string">"获取用户数据失败："</span>, error.message);
    <span class="keyword">throw</span> error;
  }
};

<span class="comment">// 调用函数获取用户数据</span>
<span class="function">fetchUserData</span>(<span class="number">1</span>)
  .<span class="function">then</span>(user => console.<span class="function">log</span>(<span class="string">"用户信息："</span>, user))
  .<span class="function">catch</span>(error => console.<span class="function">error</span>(<span class="string">"错误："</span>, error));</pre>
            </div>
            
            <div class="result-area" id="fetchGetResult">
                <p>点击"运行代码"查看结果...</p>
            </div>
            
            <h3>POST请求与JSON数据</h3>
            <div class="code-container">
                <div class="code-header">
                    <span>Fetch POST示例</span>
                    <button id="runFetchPost">运行代码</button>
                </div>
                <pre class="code-block"><span class="comment">// 创建新文章</span>
<span class="keyword">const</span> createPost = <span class="function">async</span> (postData) => {
  <span class="keyword">try</span> {
    <span class="keyword">const</span> response = <span class="keyword">await</span> <span class="function">fetch</span>(<span class="string">'https://jsonplaceholder.typicode.com/posts'</span>, {
      method: <span class="string">'POST'</span>,
      headers: {
        <span class="string">'Content-Type'</span>: <span class="string">'application/json'</span>,
        <span class="string">'Authorization'</span>: <span class="string">'Bearer YOUR_TOKEN'</span>
      },
      body: <span class="function">JSON</span>.<span class="function">stringify</span>(postData)
    });
    
    <span class="keyword">if</span> (!response.ok) {
      <span class="keyword">throw</span> <span class="keyword">new</span> <span class="function">Error</span>(<span class="string">`创建失败，状态码: ${response.status}`</span>);
    }
    
    <span class="keyword">return</span> <span class="keyword">await</span> response.<span class="function">json</span>();
  } <span class="keyword">catch</span> (error) {
    console.<span class="function">error</span>(<span class="string">"创建文章失败："</span>, error);
    <span class="keyword">throw</span> error;
  }
};

<span class="comment">// 使用示例</span>
<span class="keyword">const</span> newPost = {
  title: <span class="string">'JavaScript异步编程指南'</span>,
  body: <span class="string">'这是一篇关于JavaScript异步编程的详细指南...'</span>,
  userId: <span class="number">1</span>
};

<span class="function">createPost</span>(newPost)
  .<span class="function">then</span>(post => console.<span class="function">log</span>(<span class="string">"新文章："</span>, post))
  .<span class="function">catch</span>(error => console.<span class="function">error</span>(<span class="string">"错误："</span>, error));</pre>
            </div>
            
            <div class="result-area" id="fetchPostResult">
                <p>点击"运行代码"查看结果...</p>
            </div>
            
            <div class="notes">
                <h4>Fetch API关键点：</h4>
                <ul>
                    <li>fetch()返回一个Promise，解析为Response对象</li>
                    <li>默认使用GET方法，需要其他方法需在配置中指定</li>
                    <li>必须检查response.ok或response.status判断请求成功</li>
                    <li>response.json()返回Promise，解析为JSON对象</li>
                    <li>不支持超时设置（需使用AbortController实现）</li>
                    <li>默认不携带Cookie，需要设置credentials: 'include'</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p>JavaScript异步编程详解 &copy; 2023 | 核心概念与实例解析</p>
            <p>创建者: <a href="#">前端开发工程师</a> | 知识共享</p>
        </div>
    </div>
    
    <script>
        // DOM元素
        const runPromiseBasicBtn = document.getElementById('runPromiseBasic');
        const promiseBasicResult = document.getElementById('promiseBasicResult');
        const runPromiseChainBtn = document.getElementById('runPromiseChain');
        const promiseChainResult = document.getElementById('promiseChainResult');
        const runAsyncBasicBtn = document.getElementById('runAsyncBasic');
        const asyncBasicResult = document.getElementById('asyncBasicResult');
        const runAsyncSequenceBtn = document.getElementById('runAsyncSequence');
        const asyncSequenceResult = document.getElementById('asyncSequenceResult');
        const runFetchGetBtn = document.getElementById('runFetchGet');
        const fetchGetResult = document.getElementById('fetchGetResult');
        const runFetchPostBtn = document.getElementById('runFetchPost');
        const fetchPostResult = document.getElementById('fetchPostResult');
        
        // 1. Promise基本示例
        runPromiseBasicBtn.addEventListener('click', () => {
            promiseBasicResult.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> 正在模拟异步操作...</p>';
            
            const promiseExample = new Promise((resolve, reject) => {
                setTimeout(() => {
                    const success = Math.random() > 0.3;
                    
                    if (success) {
                        resolve("操作成功！数据已接收");
                    } else {
                        reject(new Error("请求失败：服务器错误"));
                    }
                }, 1500);
            });
            
            promiseExample
                .then(result => {
                    promiseBasicResult.innerHTML = `
                        <p style="color: #27ae60;"><i class="fas fa-check-circle"></i> 操作成功</p>
                        <p>结果: ${result}</p>
                        <p>时间: ${new Date().toLocaleTimeString()}</p>
                    `;
                })
                .catch(error => {
                    promiseBasicResult.innerHTML = `
                        <p style="color: #e74c3c;"><i class="fas fa-exclamation-circle"></i> ${error.message}</p>
                        <p>时间: ${new Date().toLocaleTimeString()}</p>
                    `;
                });
        });
        
        // 2. Promise链式调用示例
        runPromiseChainBtn.addEventListener('click', () => {
            promiseChainResult.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> 正在获取用户数据...</p>';
            
            const getUser = userId => {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({ 
                            id: userId, 
                            name: `用户${userId}`, 
                            role: 'member' 
                        });
                    }, 800);
                });
            };
            
            const getPermissions = user => {
                return new Promise(resolve => {
                    setTimeout(() => {
                        const permissions = user.role === 'admin' 
                            ? ['read', 'write', 'delete'] 
                            : ['read'];
                        resolve({ ...user, permissions });
                    }, 600);
                });
            };
            
            getUser(101)
                .then(getPermissions)
                .then(userWithPermissions => {
                    promiseChainResult.innerHTML = `
                        <p style="color: #27ae60;"><i class="fas fa-user-check"></i> 用户信息获取成功</p>
                        <p>ID: ${userWithPermissions.id}</p>
                        <p>姓名: ${userWithPermissions.name}</p>
                        <p>角色: ${userWithPermissions.role}</p>
                        <p>权限: ${userWithPermissions.permissions.join(', ')}</p>
                    `;
                })
                .catch(error => {
                    promiseChainResult.innerHTML = `
                        <p style="color: #e74c3c;"><i class="fas fa-exclamation-circle"></i> 错误: ${error.message}</p>
                    `;
                });
        });
        
        // 3. async/await基本示例
        runAsyncBasicBtn.addEventListener('click', async () => {
            asyncBasicResult.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> 正在获取数据...</p>';
            
            const fetchData = async () => {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve("数据获取成功！");
                    }, 1000);
                });
            };
            
            try {
                const data = await fetchData();
                asyncBasicResult.innerHTML = `
                    <p style="color: #27ae60;"><i class="fas fa-check-circle"></i> 操作成功</p>
                    <p>结果: ${data}</p>
                    <p>时间: ${new Date().toLocaleTimeString()}</p>
                `;
            } catch (error) {
                asyncBasicResult.innerHTML = `
                    <p style="color: #e74c3c;"><i class="fas fa-exclamation-circle"></i> ${error.message}</p>
                `;
            }
        });
        
        // 4. async/await序列操作示例
        runAsyncSequenceBtn.addEventListener('click', async () => {
            asyncSequenceResult.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> 正在加载用户数据...</p>';
            
            const login = async (email, password) => {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({ token: `token_${Math.random().toString(36).substr(2,9)}` });
                    }, 800);
                });
            };
            
            const getUserProfile = async token => {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({
                            name: "张三",
                            email: "<EMAIL>",
                            role: "admin"
                        });
                    }, 600);
                });
            };
            
            try {
                const loginData = await login("<EMAIL>", "password123");
                const userProfile = await getUserProfile(loginData.token);
                
                asyncSequenceResult.innerHTML = `
                    <p style="color: #27ae60;"><i class="fas fa-user-check"></i> 用户数据加载成功</p>
                    <p>用户名: ${userProfile.name}</p>
                    <p>邮箱: ${userProfile.email}</p>
                    <p>角色: ${userProfile.role}</p>
                    <p>Token: ${loginData.token}</p>
                `;
            } catch (error) {
                asyncSequenceResult.innerHTML = `
                    <p style="color: #e74c3c;"><i class="fas fa-exclamation-circle"></i> ${error.message}</p>
                `;
            }
        });
        
        // 5. Fetch GET示例
        runFetchGetBtn.addEventListener('click', async () => {
            fetchGetResult.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> 正在获取用户数据...</p>';
            
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/users/1');
                
                if (!response.ok) {
                    throw new Error(`请求失败，状态码: ${response.status}`);
                }
                
                const userData = await response.json();
                
                fetchGetResult.innerHTML = `
                    <p style="color: #27ae60;"><i class="fas fa-user-tie"></i> 用户信息</p>
                    <p>姓名: ${userData.name}</p>
                    <p>用户名: ${userData.username}</p>
                    <p>邮箱: ${userData.email}</p>
                    <p>公司: ${userData.company.name}</p>
                    <p>网站: <a href="http://${userData.website}" target="_blank">${userData.website}</a></p>
                `;
            } catch (error) {
                fetchGetResult.innerHTML = `
                    <p style="color: #e74c3c;"><i class="fas fa-exclamation-circle"></i> ${error.message}</p>
                    <p>请检查网络连接</p>
                `;
            }
        });
        
        // 6. Fetch POST示例
        runFetchPostBtn.addEventListener('click', async () => {
            fetchPostResult.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> 正在创建新文章...</p>';
            
            try {
                const newPost = {
                    title: 'JavaScript异步编程指南',
                    body: '这是一篇关于JavaScript异步编程的详细指南，涵盖了Promise、async/await和Fetch API等核心概念。',
                    userId: 1
                };
                
                const response = await fetch('https://jsonplaceholder.typicode.com/posts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(newPost)
                });
                
                if (!response.ok) {
                    throw new Error(`创建失败，状态码: ${response.status}`);
                }
                
                const postData = await response.json();
                
                fetchPostResult.innerHTML = `
                    <p style="color: #27ae60;"><i class="fas fa-check-circle"></i> 文章创建成功</p>
                    <p>ID: ${postData.id}</p>
                    <p>标题: ${postData.title}</p>
                    <p>内容: ${postData.body.substring(0, 60)}...</p>
                    <p>用户ID: ${postData.userId}</p>
                `;
            } catch (error) {
                fetchPostResult.innerHTML = `
                    <p style="color: #e74c3c;"><i class="fas fa-exclamation-circle"></i> ${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
