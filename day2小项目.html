<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生成绩管理系统 - 函数与作用域应用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #333;
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            padding: 30px 0;
            color: white;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        header h1 {
            font-size: 2.8rem;
            margin-bottom: 15px;
        }
        
        header p {
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
            opacity: 0.9;
        }
        
        .system-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.92);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }
        
        .panel h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
        }
        
        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .results {
            background: rgba(255, 255, 255, 0.92);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-top: 25px;
        }
        
        .results h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }
        
        .student-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .student-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 4px solid #3498db;
        }
        
        .student-info {
            flex: 1;
        }
        
        .student-name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .student-score {
            font-weight: bold;
            color: #27ae60;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .explanation {
            background: rgba(255, 255, 255, 0.92);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-top: 40px;
        }
        
        .explanation h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
            text-align: center;
        }
        
        .concept {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .concept h3 {
            color: #3498db;
            margin-bottom: 10px;
        }
        
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .highlight {
            color: #e74c3c;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .system-container {
                grid-template-columns: 1fr;
            }
            
            .stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>学生成绩管理系统</h1>
            <p>使用函数定义、参数传递、作用域和闭包实现的完整应用</p>
        </header>
        
        <div class="system-container">
            <div class="panel">
                <h2>添加学生</h2>
                <div class="form-group">
                    <label for="studentName">学生姓名</label>
                    <input type="text" id="studentName" placeholder="输入学生姓名">
                </div>
                <div class="form-group">
                    <label for="studentScore">学生成绩</label>
                    <input type="number" id="studentScore" placeholder="输入成绩 (0-100)" min="0" max="100">
                </div>
                <button id="addStudentBtn">添加学生</button>
            </div>
            
            <div class="panel">
                <h2>成绩分析</h2>
                <div class="form-group">
                    <label for="minScore">最低分筛选</label>
                    <input type="number" id="minScore" placeholder="最低分 (0-100)" min="0" max="100">
                </div>
                <div class="form-group">
                    <label for="analysisType">分析类型</label>
                    <select id="analysisType">
                        <option value="all">所有学生</option>
                        <option value="passed">及格学生 (≥60)</option>
                        <option value="excellent">优秀学生 (≥90)</option>
                    </select>
                </div>
                <button id="analyzeBtn">分析成绩</button>
            </div>
        </div>
        
        <div class="results">
            <h2>学生成绩列表</h2>
            <div class="student-list" id="studentList">
                <!-- 学生列表将在这里动态生成 -->
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <h3>平均成绩</h3>
                    <div class="stat-value" id="avgScore">0</div>
                </div>
                <div class="stat-card">
                    <h3>最高分</h3>
                    <div class="stat-value" id="maxScore">0</div>
                </div>
                <div class="stat-card">
                    <h3>最低分</h3>
                    <div class="stat-value" id="minScoreVal">0</div>
                </div>
            </div>
        </div>
        
        <div class="explanation">
            <h2>函数与作用域应用说明</h2>
            
            <div class="concept">
                <h3>1. 函数定义 - 三种方式</h3>
                <p>本项目使用了三种函数定义方式：</p>
                <div class="code-example">
                    // 函数声明（添加学生）<br>
                    function addStudent(name, score) { ... }<br><br>
                    
                    // 函数表达式（计算平均分）<br>
                    const calculateAverage = function(scores) { ... };<br><br>
                    
                    // 箭头函数（筛选学生）<br>
                    const filterStudents = (students, minScore) => { ... };
                </div>
            </div>
            
            <div class="concept">
                <h3>2. 参数传递 - 默认与剩余参数</h3>
                <p>使用默认参数处理可选参数，剩余参数处理多个学生：</p>
                <div class="code-example">
                    // 默认参数 - 设置默认成绩为60<br>
                    function createStudent(name, score = 60) { ... }<br><br>
                    
                    // 剩余参数 - 处理多个学生<br>
                    function addMultipleStudents(...newStudents) {<br>
                    &nbsp;&nbsp;newStudents.forEach(student => ...);<br>
                    }
                </div>
            </div>
            
            <div class="concept">
                <h3>3. 作用域 - 变量的可访问范围</h3>
                <p>使用函数作用域保护学生数据：</p>
                <div class="code-example">
                    // 函数作用域内的私有变量<br>
                    function createStudentSystem() {<br>
                    &nbsp;&nbsp;let students = []; // 私有变量，外部无法直接访问<br><br>
                    
                    &nbsp;&nbsp;// 返回操作接口<br>
                    &nbsp;&nbsp;return {<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;addStudent: function(name, score) { ... },<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;getStudents: function() { ... }<br>
                    &nbsp;&nbsp;};<br>
                    }
                </div>
            </div>
            
            <div class="concept">
                <h3>4. 闭包 - 封装私有数据</h3>
                <p>使用闭包创建私有学生数组：</p>
                <div class="code-example">
                    const studentSystem = (function() {<br>
                    &nbsp;&nbsp;let students = []; // 私有变量<br><br>
                    
                    &nbsp;&nbsp;// 添加学生（闭包可访问students）<br>
                    &nbsp;&nbsp;function add(name, score) { ... }<br><br>
                    
                    &nbsp;&nbsp;// 返回公共接口<br>
                    &nbsp;&nbsp;return {<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;addStudent: add,<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;getAllStudents: () => students<br>
                    &nbsp;&nbsp;};<br>
                    })();<br><br>
                    
                    // 外部只能通过公共接口访问<br>
                    studentSystem.addStudent("张三", 85);
                </div>
            </div>
        </div>
    </div>

    <script>
        // 使用闭包创建学生管理系统
        const studentSystem = (function() {
            // 私有变量 - 学生数组（外部无法直接访问）
            let students = [];
            
            // 函数声明 - 添加学生
            function addStudent(name, score = 60) {
                const newStudent = { id: Date.now(), name, score };
                students.push(newStudent);
                return newStudent;
            }
            
            // 函数表达式 - 计算平均分
            const calculateAverage = function() {
                if (students.length === 0) return 0;
                const total = students.reduce((sum, student) => sum + student.score, 0);
                return total / students.length;
            };
            
            // 箭头函数 - 查找最高分
            const findMaxScore = () => {
                if (students.length === 0) return 0;
                return Math.max(...students.map(student => student.score));
            };
            
            // 箭头函数 - 查找最低分
            const findMinScore = () => {
                if (students.length === 0) return 0;
                return Math.min(...students.map(student => student.score));
            };
            
            // 函数声明 - 筛选学生（使用默认参数）
            function filterStudents(minScore = 0) {
                return students.filter(student => student.score >= minScore);
            }
            
            // 使用剩余参数 - 批量添加学生
            function addMultipleStudents(...newStudents) {
                newStudents.forEach(student => {
                    addStudent(student.name, student.score);
                });
                return newStudents.length;
            }
            
            // 返回公共接口（闭包）
            return {
                addStudent,
                getAllStudents: () => [...students], // 返回副本保护数据
                calculateAverage,
                findMaxScore,
                findMinScore,
                filterStudents,
                addMultipleStudents
            };
        })();
        
        // 初始化演示数据
        studentSystem.addMultipleStudents(
            { name: "张三", score: 85 },
            { name: "李四", score: 92 },
            { name: "王五", score: 78 },
            { name: "赵六", score: 65 },
            { name: "钱七", score: 58 }
        );
        
        // DOM操作和事件处理
        document.addEventListener('DOMContentLoaded', function() {
            const studentList = document.getElementById('studentList');
            const addStudentBtn = document.getElementById('addStudentBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const avgScoreEl = document.getElementById('avgScore');
            const maxScoreEl = document.getElementById('maxScore');
            const minScoreEl = document.getElementById('minScoreVal');
            
            // 渲染学生列表
            function renderStudents(students = studentSystem.getAllStudents()) {
                studentList.innerHTML = '';
                
                if (students.length === 0) {
                    studentList.innerHTML = '<p class="no-students">暂无学生数据</p>';
                    return;
                }
                
                students.forEach(student => {
                    const studentItem = document.createElement('div');
                    studentItem.className = 'student-item';
                    studentItem.innerHTML = `
                        <div class="student-info">
                            <div class="student-name">${student.name}</div>
                        </div>
                        <div class="student-score">${student.score}分</div>
                    `;
                    studentList.appendChild(studentItem);
                });
            }
            
            // 更新统计信息
            function updateStats() {
                avgScoreEl.textContent = studentSystem.calculateAverage().toFixed(1);
                maxScoreEl.textContent = studentSystem.findMaxScore();
                minScoreEl.textContent = studentSystem.findMinScore();
            }
            
            // 添加学生事件
            addStudentBtn.addEventListener('click', function() {
                const nameInput = document.getElementById('studentName');
                const scoreInput = document.getElementById('studentScore');
                
                const name = nameInput.value.trim();
                const score = parseInt(scoreInput.value);
                
                if (!name) {
                    alert('请输入学生姓名');
                    return;
                }
                
                if (isNaN(score) || score < 0 || score > 100) {
                    alert('请输入0-100之间的有效成绩');
                    return;
                }
                
                // 调用闭包函数添加学生
                studentSystem.addStudent(name, score);
                
                // 重新渲染列表和统计
                renderStudents();
                updateStats();
                
                // 清空输入
                nameInput.value = '';
                scoreInput.value = '';
                nameInput.focus();
            });
            
            // 分析成绩事件
            analyzeBtn.addEventListener('click', function() {
                const minScoreInput = document.getElementById('minScore');
                const analysisType = document.getElementById('analysisType').value;
                
                let minScore = 0;
                
                if (minScoreInput.value) {
                    minScore = parseInt(minScoreInput.value);
                    if (isNaN(minScore) || minScore < 0 || minScore > 100) {
                        alert('请输入0-100之间的有效成绩');
                        return;
                    }
                }
                
                let filteredStudents = [];
                
                // 根据分析类型筛选
                switch(analysisType) {
                    case 'all':
                        filteredStudents = studentSystem.filterStudents(minScore);
                        break;
                    case 'passed':
                        filteredStudents = studentSystem.filterStudents(Math.max(minScore, 60));
                        break;
                    case 'excellent':
                        filteredStudents = studentSystem.filterStudents(Math.max(minScore, 90));
                        break;
                }
                
                renderStudents(filteredStudents);
            });
            
            // 初始渲染
            renderStudents();
            updateStats();
        });
    </script>
</body>
</html>