<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>JavaScript基础语法（Day 1）- 小白专属</title>
    <style>
        /* 页面基础样式 */
        body {
            font-family: "微软雅黑", Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            padding: 0 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        /* 章节标题样式 */
        .chapter-title {
            color: #2f4f4f;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        /* 知识点列表样式 */
        .knowledge-points {
            list-style-type: square;
            padding-left: 20px;
            color: #333;
        }
        .knowledge-points li {
            margin: 10px 0;
        }
        /* 代码块样式（关键：使用 pre 标签保留格式） */
        .code-block {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: "Consolas", "Courier New", monospace;
            font-size: 14px;
            line-height: 1.8;
            white-space: pre-wrap; /* 保留换行和空格，自动换行 */
        }
        /* 运行按钮样式 */
        .run-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        .run-btn:hover {
            background-color: #45a049;
        }
        /* 结果显示样式 */
        .result-box {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #c8e6c9;
            color: #2e7d32;
            font-family: "Consolas", "Courier New", monospace;
            white-space: pre-wrap; /* 结果保留换行 */
        }
        /* 注意事项样式 */
        .tips-section {
            background-color: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 40px;
            border: 1px solid #ffeeba;
        }
        .tips-title {
            color: #856404;
            margin-top: 0;
        }
        .tips-list {
            list-style-type: disc;
            padding-left: 20px;
            color: #5a3e03;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #4CAF50;">JavaScript基础语法（Day 1）</h1>
        <p style="text-align: center; color: #666;">适合小白的入门教程 | 清晰排版 + 可运行代码</p>

        <!-- 1. 变量声明章节 -->
        <section class="chapter">
            <h2 class="chapter-title">一、变量声明 - let/const/var的区别</h2>
            <ul class="knowledge-points">
                <li><strong>var</strong>：旧版关键字，函数作用域，变量提升，可重复声明（不推荐）</li>
                <li><strong>let</strong>：ES6新增，块级作用域（{}内有效），无变量提升，不可重复声明（推荐用于变量）</li>
                <li><strong>const</strong>：ES6新增，块级作用域，不可重新赋值（推荐用于常量，对象属性可修改）</li>
            </ul>

            <!-- 代码块：使用 pre 标签保留格式，缩进2空格，逻辑块换行 -->
            <pre class="code-block">
// 1. var的变量提升（坑）：声明提前，赋值不提前
console.log(a);  // 输出undefined（此时a已声明，但未赋值）
var a = 10;      // 赋值操作保留在原位


// 2. let的块级作用域（正确用法）
if (true) {
  let b = 20;        // 块内声明let变量
  console.log(b);    // 输出20（块内可访问）
}
// console.log(b);    // 报错：b is not defined（块外不可访问）


// 3. const的常量特性（不能重新赋值）
const c = 30;        // 声明并赋值常量
// c = 40;           // 报错：Assignment to constant variable（禁止重新赋值）


// 4. const修饰对象（可修改属性，因为存储的是引用地址）
const user = { 
  name: '小白'       // 对象属性用英文引号（避免语法错误）
};
user.name = '小明';  // 允许修改对象属性（未改变引用地址）
console.log(user.name);  // 输出'小明'（属性修改成功）
            </pre>

            <button class="run-btn" onclick="runVarExample()">运行代码</button>
            <div class="result-box" id="varResult" style="display: none;"></div>
        </section>

        <!-- 2. 数据类型章节 -->
        <section class="chapter">
            <h2 class="chapter-title">二、数据类型 - 基本类型vs引用类型</h2>
            <ul class="knowledge-points">
                <li><strong>基本类型</strong>：值存储（栈内存），包括string/number/boolean/null/undefined（复制时复制值，修改不影响原变量）</li>
                <li><strong>引用类型</strong>：地址存储（堆内存），包括object/array/function（复制时复制地址，修改影响原变量）</li>
            </ul>

            <pre class="code-block">
// 1. 基本类型（复制值，修改不影响原变量）
let num1 = 10;
let num2 = num1;      // 复制num1的值（10）
num2 = 20;            // 修改num2
console.log('num1:', num1);  // 输出10（原变量不变）
console.log('num2:', num2);  // 输出20（新变量改变）


// 2. 引用类型（复制地址，修改影响原变量）
let arr1 = [1, 2, 3];
let arr2 = arr1;      // 复制arr1的地址（指向同一个数组）
arr2.push(4);         // 修改arr2（添加元素4）
console.log('arr1:', arr1);  // 输出[1,2,3,4]（原数组被影响）
console.log('arr2:', arr2);  // 输出[1,2,3,4]（新数组改变）


// 3. 判断数据类型（typeof/instanceof）
console.log('typeof "hello":', typeof "hello");  // 输出string
console.log('[] instanceof Array:', [] instanceof Array);  // 输出true
            </pre>

            <button class="run-btn" onclick="runTypeExample()">运行代码</button>
            <div class="result-box" id="typeResult" style="display: none;"></div>
        </section>

        <!-- 3. 运算符章节 -->
        <section class="chapter">
            <h2 class="chapter-title">三、运算符 - 算术/比较/逻辑</h2>
            <ul class="knowledge-points">
                <li><strong>算术运算符</strong>：+（加/拼接字符串）、-、*、/、%（取余）、++（自增）</li>
                <li><strong>比较运算符</strong>：===（严格相等，推荐）、==（相等，类型转换）、>、<</li>
                <li><strong>逻辑运算符</strong>：&&（与，短路求值）、||（或，短路求值）、!（非）</li>
            </ul>

            <pre class="code-block">
// 1. 算术运算符（注意字符串拼接）
console.log('1+2:', 1 + 2);        // 输出3（数字相加）
console.log('"1"+2:', "1" + 2);    // 输出"12"（字符串拼接，+号遇到字符串变拼接）
console.log('5-3:', 5 - 3);        // 输出2
console.log('5*3:', 5 * 3);        // 输出15
console.log('5/2:', 5 / 2);        // 输出2.5（JS除法不会取整）
console.log('5%2:', 5 % 2);        // 输出1（取余，5除以2余1）


// 2. 比较运算符（推荐用===，避免类型转换坑）
console.log('10==="10":', 10 === "10");  // false（类型不同：数字vs字符串）
console.log('0===false:', 0 === false);  // false（类型不同：数字vs布尔）
console.log('5>3:', 5 > 3);              // true
console.log('5<3:', 5 < 3);              // false


// 3. 逻辑运算符（短路求值：提前终止计算）
// &&：左边为false，右边不执行
console.log('false&&"右边不执行":', false && "右边不执行");  // 输出false

// ||：左边为true，右边不执行
console.log('true||"右边不执行":', true || "右边不执行");    // 输出true

// !：取反（将值转为布尔后取反）
console.log('!true:', !true);    // 输出false
console.log('!0:', !0);          // 输出true（0是false，取反为true）
console.log('!""', !"");         // 输出true（空字符串是false，取反为true）
            </pre>

            <button class="run-btn" onclick="runOperatorExample()">运行代码</button>
            <div class="result-box" id="operatorResult" style="display: none;"></div>
        </section>

        <!-- 4. 条件语句章节 -->
        <section class="chapter">
            <h2 class="chapter-title">四、条件语句 - if/else/switch</h2>
            <ul class="knowledge-points">
                <li><strong>if/else</strong>：判断条件是否成立（比如成绩等级），可嵌套</li>
                <li><strong>switch</strong>：判断变量是否等于固定值（比如星期几），需加break（避免穿透）</li>
            </ul>

            <pre class="code-block">
// 1. if/else判断成绩等级（多条件分支）
let score = 85;
if (score >= 90) {
  console.log('成绩：优秀');
} else if (score >= 80) {
  console.log('成绩：良好');  // 输出（85 >= 80）
} else if (score >= 60) {
  console.log('成绩：及格');
} else {
  console.log('成绩：不及格');
}


// 2. switch判断星期几（固定值匹配）
let day = 3;  // 假设1=周一，2=周二，...，7=周日
switch (day) {
  case 1:
    console.log('星期：周一');
    break;  // 终止当前case，避免穿透
  case 2:
    console.log('星期：周二');
    break;
  case 3:
    console.log('星期：周三');  // 输出（day=3）
    break;
  default:
    console.log('星期：周末');  // 所有case不匹配时执行
}


// 3. switch的穿透问题（忘记加break）
let fruit = '苹果';
switch (fruit) {
  case '苹果':
    console.log('苹果');  // 输出
  case '香蕉':
    console.log('香蕉');  // 继续输出（因为苹果的case没加break）
  default:
    console.log('其他水果');  // 继续输出
}
            </pre>

            <button class="run-btn" onclick="runConditionExample()">运行代码</button>
            <div class="result-box" id="conditionResult" style="display: none;"></div>
        </section>

        <!-- 5. 循环语句章节 -->
        <section class="chapter">
            <h2 class="chapter-title">五、循环语句 - for/while/forEach</h2>
            <ul class="knowledge-points">
                <li><strong>for循环</strong>：已知循环次数（比如遍历数组、打印1-10）</li>
                <li><strong>while循环</strong>：未知循环次数（比如直到用户输入正确）</li>
                <li><strong>forEach</strong>：数组遍历方法（更简洁，ES5新增）</li>
            </ul>

            <pre class="code-block">
// 1. for循环：已知次数（打印1-5）
// 结构：for (初始化变量; 循环条件; 增量/减量) { ... }
for (let i = 1; i <= 5; i++) {
  console.log('for循环：', i);  // 输出1、2、3、4、5
}


// 2. while循环：未知次数（计算1-100的和）
// 结构：while (循环条件) { ... }（条件为true时执行）
let sum = 0;
let i = 1;
while (i <= 100) {
  sum += i;  // 等价于 sum = sum + i
  i++;       // 等价于 i = i + 1（避免死循环）
}
console.log('1-100的和：', sum);  // 输出5050


// 3. do-while循环：至少执行一次（先执行再判断条件）
// 结构：do { ... } while (循环条件);
let password;
do {
  password = prompt('请输入密码（至少6位）');  // 弹出输入框
} while (password.length < 6);  // 密码长度<6则重复执行
console.log('密码设置成功：', password);


// 4. forEach：数组遍历（更简洁，接收回调函数）
let fruits = ['苹果', '香蕉', '橘子'];
fruits.forEach((item, index) => {  // item=当前元素，index=当前索引
  console.log(`forEach：索引${index}是${item}`);  // 输出每个元素
});
            </pre>

            <button class="run-btn" onclick="runLoopExample()">运行代码</button>
            <div class="result-box" id="loopResult" style="display: none;"></div>
        </section>

        <!-- 小白注意事项 -->
        <section class="tips-section">
            <h3 class="tips-title">小白学习注意事项</h3>
            <ul class="tips-list">
                <li>变量声明优先用<strong>let</strong>（变量）和<strong>const</strong>（常量），尽量不用var（避免变量提升的坑）</li>
                <li>比较相等时用<strong>===</strong>（严格相等），不要用==（会自动类型转换，比如0==false会返回true）</li>
                <li>switch语句一定要加<strong>break</strong>，否则会“穿透”（比如case 3没break，会继续执行case 4）</li>
                <li>循环时注意<strong>终止条件</strong>（比如while循环的i++不能漏，否则会无限循环，浏览器会崩）</li>
                <li>调试代码用<strong>console.log(变量名)</strong>，比如不确定sum是否正确，就打印sum看看中间值</li>
            </ul>
        </section>

        <div style="text-align:center;margin:30px 0;">
            <a href="day2.html" style="font-size:18px;color:#2196F3;text-decoration:none;border:1px solid #2196F3;padding:8px 20px;border-radius:5px;transition:background 0.2s;">下一天：Day 2 &rarr;</a>
        </div>
    </div>

    <!-- 运行代码的JavaScript逻辑（保持不变，确保结果正常显示） -->
    <script>
        function runVarExample() {
            const resultBox = document.getElementById('varResult');
            let output = '';
            try {
                var a; let b; const c = 30; const user = { name: '小白' };
                output += 'console.log(a); // ' + (a = 10, console.log(a) || a) + '\n';
                output += 'let b = 20; console.log(b); // ' + (b = 20, console.log(b) || b) + '\n';
                output += 'const c = 30; // 不能重新赋值\n';
                output += 'user.name = "小明"; console.log(user.name); // ' + (user.name = '小明', console.log(user.name) || user.name) + '\n';
            } catch (e) { output = '运行错误：' + e.message; }
            resultBox.textContent = output; resultBox.style.display = 'block';
        }

        function runTypeExample() {
            const resultBox = document.getElementById('typeResult');
            let output = '';
            try {
                let num1 = 10, num2 = num1; num2 = 20;
                output += 'num1: ' + num1 + '\nnum2: ' + num2 + '\n';
                let arr1 = [1,2,3], arr2 = arr1; arr2.push(4);
                output += 'arr1: ' + JSON.stringify(arr1) + '\narr2: ' + JSON.stringify(arr2) + '\n';
                output += 'typeof "hello": ' + typeof "hello" + '\n[] instanceof Array: ' + ([] instanceof Array);
            } catch (e) { output = '运行错误：' + e.message; }
            resultBox.textContent = output; resultBox.style.display = 'block';
        }

        function runOperatorExample() {
            const resultBox = document.getElementById('operatorResult');
            let output = '';
            try {
                output += '1+2: ' + (1+2) + '\n';
                output += '"1"+2: ' + ("1"+2) + '\n';
                output += '5-3: ' + (5-3) + '\n';
                output += '5*3: ' + (5*3) + '\n';
                output += '5/2: ' + (5/2) + '\n';
                output += '5%2: ' + (5%2) + '\n';
                output += '10==="10": ' + (10==="10") + '\n';
                output += '0===false: ' + (0===false) + '\n';
                output += 'false&&"右边不执行": ' + (false&&"右边不执行") + '\n';
                output += '!0: ' + (!0) + '\n';
            } catch (e) { output = '运行错误：' + e.message; }
            resultBox.textContent = output; resultBox.style.display = 'block';
        }

        function runConditionExample() {
            const resultBox = document.getElementById('conditionResult');
            let output = '';
            try {
                let score = 85;
                if (score >=90) output += '成绩：优秀\n';
                else if (score >=80) output += '成绩：良好\n';
                else if (score >=60) output += '成绩：及格\n';
                else output += '成绩：不及格\n';

                let day = 3;
                switch(day) {
                    case 1: output += '星期：周一\n'; break;
                    case 3: output += '星期：周三\n'; break;
                    default: output += '星期：周末\n';
                }

                let fruit = '苹果';
                output += 'switch穿透示例：\n';
                switch(fruit) {
                    case '苹果': output += '  苹果\n';
                    case '香蕉': output += '  香蕉\n';
                    default: output += '  其他水果\n';
                }
            } catch (e) { output = '运行错误：' + e.message; }
            resultBox.textContent = output; resultBox.style.display = 'block';
        }

        function runLoopExample() {
            const resultBox = document.getElementById('loopResult');
            let output = '';
            try {
                output += 'for循环打印1-5：\n';
                for (let i=1; i<=5; i++) output += '  ' + i + '\n';

                let sum = 0, i=1;
                while (i<=100) { sum +=i; i++; }
                output += '1-100的和：' + sum + '\n';

                let fruits = ['苹果', '香蕉', '橘子'];
                output += 'forEach遍历数组：\n';
                fruits.forEach((item, index) => output += `  索引${index}：${item}\n`);
            } catch (e) { output = '运行错误：' + e.message; }
            resultBox.textContent = output; resultBox.style.display = 'block';
        }
    </script>
</body>
</html>