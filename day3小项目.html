<<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生成绩管理系统 - Day3 数组与对象实践</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        h1 {
            font-size: 2.8rem;
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #ffd700;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }
        
        @media (max-width: 900px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 25px;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
        }
        
        .card-title i {
            background: #3498db;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        
        h2 {
            font-size: 1.8rem;
            color: #2c3e50;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        input, select {
            width: 100%;
            padding: 12px 15px;
            border-radius: 8px;
            border: 2px solid #ddd;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            background: linear-gradient(90deg, #3498db, #2c3e50);
            color: white;
            border: none;
            padding: 14px 25px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn i {
            margin-right: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #7f8c8d;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        th {
            background: #2c3e50;
            color: white;
            padding: 16px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 14px 16px;
            border-bottom: 1px solid #eee;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        .subject-cell {
            display: flex;
            justify-content: space-between;
        }
        
        .subject-name {
            font-weight: 500;
        }
        
        .subject-grade {
            font-weight: bold;
            color: #3498db;
        }
        
        .average-cell {
            font-weight: bold;
            background: rgba(52, 152, 219, 0.1);
            color: #2c3e50;
        }
        
        .highlight {
            background: #f1c40f;
            color: white;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            color: white;
            padding: 20px;
            font-size: 1.1rem;
        }
        
        .footer a {
            color: #f1c40f;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .actions button {
            flex: 1;
        }
        
        .btn-remove {
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }
        
        .btn-calculate {
            background: linear-gradient(90deg, #2ecc71, #27ae60);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-graduation-cap"></i> 学生成绩管理系统</h1>
            <p class="subtitle">使用JavaScript数组与对象实现学生数据的存储、查找、统计和排序功能</p>
        </header>
        
        <div class="content-grid">
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-user-plus"></i>
                    <h2>添加新学生</h2>
                </div>
                
                <div class="input-group">
                    <label for="studentId">学号</label>
                    <input type="text" id="studentId" placeholder="例如: S2023001">
                </div>
                
                <div class="input-group">
                    <label for="studentName">姓名</label>
                    <input type="text" id="studentName" placeholder="例如: 张三">
                </div>
                
                <div class="input-group">
                    <label for="mathGrade">数学成绩</label>
                    <input type="number" id="mathGrade" placeholder="0-100" min="0" max="100">
                </div>
                
                <div class="input-group">
                    <label for="englishGrade">英语成绩</label>
                    <input type="number" id="englishGrade" placeholder="0-100" min="0" max="100">
                </div>
                
                <div class="input-group">
                    <label for="scienceGrade">科学成绩</label>
                    <input type="number" id="scienceGrade" placeholder="0-100" min="0" max="100">
                </div>
                
                <button class="btn" id="addStudentBtn">
                    <i class="fas fa-plus-circle"></i> 添加学生
                </button>
            </div>
            
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-search"></i>
                    <h2>查找与统计</h2>
                </div>
                
                <div class="input-group">
                    <label for="searchInput">按学号或姓名查找</label>
                    <input type="text" id="searchInput" placeholder="输入学号或姓名">
                </div>
                
                <div class="actions">
                    <button class="btn" id="searchBtn">
                        <i class="fas fa-search"></i> 查找
                    </button>
                    <button class="btn btn-remove" id="clearSearchBtn">
                        <i class="fas fa-times"></i> 清除
                    </button>
                </div>
                
                <div class="input-group">
                    <label>排序方式</label>
                    <select id="sortBy">
                        <option value="id">按学号</option>
                        <option value="name">按姓名</option>
                        <option value="math">按数学成绩</option>
                        <option value="english">按英语成绩</option>
                        <option value="science">按科学成绩</option>
                        <option value="average">按平均分</option>
                    </select>
                </div>
                
                <div class="actions">
                    <button class="btn" id="sortAscBtn">
                        <i class="fas fa-sort-amount-up"></i> 升序
                    </button>
                    <button class="btn btn-remove" id="sortDescBtn">
                        <i class="fas fa-sort-amount-down"></i> 降序
                    </button>
                </div>
                
                <div class="input-group">
                    <button class="btn btn-calculate" id="calculateStatsBtn">
                        <i class="fas fa-calculator"></i> 计算统计数据
                    </button>
                </div>
            </div>
        </div>
        
        <div class="stats-grid" id="statsContainer">
            <!-- 统计数据将动态生成 -->
        </div>
        
        <div class="card">
            <div class="card-title">
                <i class="fas fa-list"></i>
                <h2>学生成绩列表</h2>
            </div>
            
            <table id="studentsTable">
                <thead>
                    <tr>
                        <th>学号</th>
                        <th>姓名</th>
                        <th>数学</th>
                        <th>英语</th>
                        <th>科学</th>
                        <th>平均分</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="studentsBody">
                    <!-- 学生数据将动态生成 -->
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>本项目展示了JavaScript中数组与对象的实际应用 | Day3 学习成果 | &copy; 2023 JavaScript学习项目</p>
            <p>创建者: <a href="#">前端开发者</a> | 使用技术: HTML, CSS, JavaScript</p>
        </div>
    </div>
    
    <script>
        // 学生数据结构
        const students = [
            {
                id: "S2023001",
                name: "张三",
                grades: {
                    math: 89,
                    english: 92,
                    science: 85
                }
            },
            {
                id: "S2023002",
                name: "李四",
                grades: {
                    math: 78,
                    english: 85,
                    science: 90
                }
            },
            {
                id: "S2023003",
                name: "王五",
                grades: {
                    math: 95,
                    english: 88,
                    science: 92
                }
            },
            {
                id: "S2023004",
                name: "赵六",
                grades: {
                    math: 82,
                    english: 79,
                    science: 85
                }
            }
        ];
        
        // DOM元素
        const studentsBody = document.getElementById('studentsBody');
        const addStudentBtn = document.getElementById('addStudentBtn');
        const searchBtn = document.getElementById('searchBtn');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        const sortAscBtn = document.getElementById('sortAscBtn');
        const sortDescBtn = document.getElementById('sortDescBtn');
        const calculateStatsBtn = document.getElementById('calculateStatsBtn');
        const statsContainer = document.getElementById('statsContainer');
        const sortBy = document.getElementById('sortBy');
        
        // 渲染学生表格
        function renderStudents(data = students) {
            studentsBody.innerHTML = '';
            
            data.forEach(student => {
                const avg = calculateAverage(student.grades);
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${student.id}</td>
                    <td>${student.name}</td>
                    <td class="subject-cell">
                        <span class="subject-name">数学</span>
                        <span class="subject-grade">${student.grades.math}</span>
                    </td>
                    <td class="subject-cell">
                        <span class="subject-name">英语</span>
                        <span class="subject-grade">${student.grades.english}</span>
                    </td>
                    <td class="subject-cell">
                        <span class="subject-name">科学</span>
                        <span class="subject-grade">${student.grades.science}</span>
                    </td>
                    <td class="average-cell">${avg.toFixed(1)}</td>
                    <td>
                        <button class="btn btn-remove" onclick="removeStudent('${student.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </td>
                `;
                
                studentsBody.appendChild(row);
            });
        }
        
        // 计算单个学生平均分
        function calculateAverage(grades) {
            const values = Object.values(grades);
            return values.reduce((sum, grade) => sum + grade, 0) / values.length;
        }
        
        // 计算统计数据
        function calculateStatistics() {
            const mathGrades = students.map(student => student.grades.math);
            const englishGrades = students.map(student => student.grades.english);
            const scienceGrades = students.map(student => student.grades.science);
            const averages = students.map(student => calculateAverage(student.grades));
            
            // 计算各科平均分
            const mathAvg = mathGrades.reduce((a, b) => a + b, 0) / mathGrades.length;
            const englishAvg = englishGrades.reduce((a, b) => a + b, 0) / englishGrades.length;
            const scienceAvg = scienceGrades.reduce((a, b) => a + b, 0) / scienceGrades.length;
            const overallAvg = averages.reduce((a, b) => a + b, 0) / averages.length;
            
            // 计算最高分
            const maxMath = Math.max(...mathGrades);
            const maxEnglish = Math.max(...englishGrades);
            const maxScience = Math.max(...scienceGrades);
            const maxAvg = Math.max(...averages);
            
            // 计算最低分
            const minMath = Math.min(...mathGrades);
            const minEnglish = Math.min(...englishGrades);
            const minScience = Math.min(...scienceGrades);
            const minAvg = Math.min(...averages);
            
            // 生成统计HTML
            statsContainer.innerHTML = `
                <div class="stat-card">
                    <div class="stat-label">数学平均分</div>
                    <div class="stat-value">${mathAvg.toFixed(1)}</div>
                    <div>最高: ${maxMath} | 最低: ${minMath}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">英语平均分</div>
                    <div class="stat-value">${englishAvg.toFixed(1)}</div>
                    <div>最高: ${maxEnglish} | 最低: ${minEnglish}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">科学平均分</div>
                    <div class="stat-value">${scienceAvg.toFixed(1)}</div>
                    <div>最高: ${maxScience} | 最低: ${minScience}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">总平均分</div>
                    <div class="stat-value">${overallAvg.toFixed(1)}</div>
                    <div>最高: ${maxAvg.toFixed(1)} | 最低: ${minAvg.toFixed(1)}</div>
                </div>
            `;
        }
        
        // 添加学生
        function addStudent() {
            const id = document.getElementById('studentId').value.trim();
            const name = document.getElementById('studentName').value.trim();
            const math = parseInt(document.getElementById('mathGrade').value);
            const english = parseInt(document.getElementById('englishGrade').value);
            const science = parseInt(document.getElementById('scienceGrade').value);
            
            // 验证输入
            if (!id || !name || isNaN(math) || isNaN(english) || isNaN(science)) {
                alert('请填写完整的表单！');
                return;
            }
            
            // 检查学号是否重复
            if (students.some(student => student.id === id)) {
                alert('该学号已存在！');
                return;
            }
            
            // 添加新学生
            students.push({
                id,
                name,
                grades: {
                    math,
                    english,
                    science
                }
            });
            
            // 清空表单
            document.getElementById('studentId').value = '';
            document.getElementById('studentName').value = '';
            document.getElementById('mathGrade').value = '';
            document.getElementById('englishGrade').value = '';
            document.getElementById('scienceGrade').value = '';
            
            // 重新渲染
            renderStudents();
            alert(`学生 ${name} 添加成功！`);
        }
        
        // 删除学生
        function removeStudent(id) {
            if (confirm('确定要删除这名学生吗？')) {
                const index = students.findIndex(student => student.id === id);
                if (index !== -1) {
                    const name = students[index].name;
                    students.splice(index, 1);
                    renderStudents();
                    alert(`学生 ${name} 已删除！`);
                }
            }
        }
        
        // 搜索学生
        function searchStudents() {
            const query = document.getElementById('searchInput').value.trim().toLowerCase();
            
            if (!query) {
                renderStudents();
                return;
            }
            
            const results = students.filter(student => 
                student.id.toLowerCase().includes(query) || 
                student.name.toLowerCase().includes(query)
            );
            
            if (results.length === 0) {
                studentsBody.innerHTML = `<tr><td colspan="7" style="text-align:center;padding:30px;">没有找到匹配的学生</td></tr>`;
            } else {
                renderStudents(results);
            }
        }
        
        // 清除搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            renderStudents();
        }
        
        // 排序学生
        function sortStudents(order = 'asc') {
            const sortField = sortBy.value;
            
            const sortedStudents = [...students].sort((a, b) => {
                let valueA, valueB;
                
                if (sortField === 'id' || sortField === 'name') {
                    valueA = a[sortField];
                    valueB = b[sortField];
                } else if (sortField === 'average') {
                    valueA = calculateAverage(a.grades);
                    valueB = calculateAverage(b.grades);
                } else {
                    valueA = a.grades[sortField];
                    valueB = b.grades[sortField];
                }
                
                if (valueA < valueB) return order === 'asc' ? -1 : 1;
                if (valueA > valueB) return order === 'asc' ? 1 : -1;
                return 0;
            });
            
            renderStudents(sortedStudents);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            renderStudents();
            
            // 添加事件监听器
            addStudentBtn.addEventListener('click', addStudent);
            searchBtn.addEventListener('click', searchStudents);
            clearSearchBtn.addEventListener('click', clearSearch);
            sortAscBtn.addEventListener('click', () => sortStudents('asc'));
            sortDescBtn.addEventListener('click', () => sortStudents('desc'));
            calculateStatsBtn.addEventListener('click', calculateStatistics);
        });
    </script>
</body>
</html>!-- day3小项目.html -->
