<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ES6+特性实战 - 现代化任务管理器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header class="app-header">
            <h1><i class="fas fa-tasks"></i> ES6+任务管理器</h1>
            <p>使用现代JavaScript特性构建的任务管理应用</p>
        </header>

        <div class="main-content">
            <!-- 任务输入区域 -->
            <div class="task-input-section">
                <div class="input-group">
                    <input type="text" id="taskInput" placeholder="输入新任务..." maxlength="100">
                    <select id="prioritySelect">
                        <option value="low">低优先级</option>
                        <option value="medium" selected>中优先级</option>
                        <option value="high">高优先级</option>
                    </select>
                    <button id="addTaskBtn"><i class="fas fa-plus"></i> 添加任务</button>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats-section">
                <div class="stat-card">
                    <i class="fas fa-list"></i>
                    <div>
                        <span class="stat-number" id="totalTasks">0</span>
                        <span class="stat-label">总任务</span>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <span class="stat-number" id="completedTasks">0</span>
                        <span class="stat-label">已完成</span>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-clock"></i>
                    <div>
                        <span class="stat-number" id="pendingTasks">0</span>
                        <span class="stat-label">待完成</span>
                    </div>
                </div>
            </div>

            <!-- 过滤器 -->
            <div class="filter-section">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="pending">待完成</button>
                <button class="filter-btn" data-filter="completed">已完成</button>
                <button class="filter-btn" data-filter="high">高优先级</button>
            </div>

            <!-- 任务列表 -->
            <div class="task-list" id="taskList">
                <!-- 任务项将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入模块化JavaScript -->
    <script type="module" src="main.js"></script>
</body>
</html>
