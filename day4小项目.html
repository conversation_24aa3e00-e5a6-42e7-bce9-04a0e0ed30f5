<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时天气预报应用</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 完整CSS样式保持不变（已省略重复内容，实际使用时保留全部样式） */
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: 'Segoe UI', sans-serif; }
        body { background: linear-gradient(135deg, #1a2980, #26d0ce); color: #333; min-height: 100vh; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        header { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 15px; padding: 30px; margin-bottom: 30px; text-align: center; }
        h1 { color: white; font-size: 2.8rem; margin-bottom: 12px; }
        .weather-app { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.15); }
        .search-box { display: flex; max-width: 500px; margin-bottom: 20px; }
        .search-box input { flex: 1; padding: 15px 20px; border: none; font-size: 1.1rem; outline: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-radius: 50px 0 0 50px; }
        .search-box button { background: linear-gradient(135deg, #6a11cb, #2575fc); color: white; border: none; padding: 0 25px; border-radius: 0 50px 50px 0; cursor: pointer; }
        .location-btn { background: #2ecc71; color: white; border: none; padding: 12px 20px; border-radius: 50px; cursor: pointer; margin-left: 10px; }
        .weather-card { background: linear-gradient(135deg, #6a11cb, #2575fc); color: white; border-radius: 15px; padding: 25px; margin-bottom: 20px; }
        .temperature { font-size: 4rem; font-weight: 700; }
        .weather-details { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-top: 20px; }
        .detail-item { background: rgba(255,255,255,0.15); padding: 12px 15px; border-radius: 10px; display: flex; align-items: center; gap: 10px; }
        .forecast-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)); gap: 20px; margin-top: 20px; }
        .forecast-item { background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .loading { text-align: center; padding: 50px; }
        .loading i { font-size: 3rem; color: #3498db; animation: spin 1.5s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .error-message { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0; display: none; }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-cloud-sun"></i> 实时天气预报</h1>
        </header>
        
        <div class="weather-app">
            <div class="app-header">
                <div class="search-container">
                    <div class="search-box">
                        <input type="text" id="cityInput" placeholder="输入城市名称..." value="北京">
                        <button id="searchBtn"><i class="fas fa-search"></i> 查询</button>
                    </div>
                </div>
                <button class="location-btn" id="locationBtn">
                    <i class="fas fa-location-arrow"></i> 使用当前位置
                </button>
            </div>
            
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-circle"></i> 
                <span id="errorText">错误信息</span>
            </div>
            
            <div id="weatherContainer">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>正在加载天气数据...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 已集成您的API密钥（关键步骤！）
        const API_KEY = '7986166947e655225a4cbfe2df599d95'; 
        const BASE_URL = 'https://api.openweathermap.org/data/2.5';
        
        // DOM元素
        const cityInput = document.getElementById('cityInput');
        const searchBtn = document.getElementById('searchBtn');
        const locationBtn = document.getElementById('locationBtn');
        const weatherContainer = document.getElementById('weatherContainer');
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        // 天气图标映射
        const weatherIcons = {
            '01d': 'fas fa-sun', '01n': 'fas fa-moon', '02d': 'fas fa-cloud-sun', '02n': 'fas fa-cloud-moon',
            '03d': 'fas fa-cloud', '03n': 'fas fa-cloud', '04d': 'fas fa-cloud', '04n': 'fas fa-cloud',
            '09d': 'fas fa-cloud-rain', '09n': 'fas fa-cloud-rain', '10d': 'fas fa-cloud-sun-rain', '10n': 'fas fa-cloud-moon-rain',
            '11d': 'fas fa-bolt', '11n': 'fas fa-bolt', '13d': 'fas fa-snowflake', '13n': 'fas fa-snowflake',
            '50d': 'fas fa-smog', '50n': 'fas fa-smog'
        };
        
        // 星期名称映射
        const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        
        // 初始化：默认加载北京天气
        document.addEventListener('DOMContentLoaded', () => {
            fetchWeather('北京');
            
            // 绑定事件
            searchBtn.addEventListener('click', () => fetchWeather(cityInput.value.trim()));
            cityInput.addEventListener('keypress', (e) => e.key === 'Enter' && fetchWeather(cityInput.value.trim()));
            locationBtn.addEventListener('click', getLocationWeather);
        });
        
        // 通过城市名获取天气（核心函数：已使用您的API密钥）
        async function fetchWeather(city) {
            if (!city) { showError('请输入城市名称'); return; }
            
            weatherContainer.innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>正在获取 ${city} 的天气数据...</p>
                </div>
            `;
            hideError();

            try {
                // 1. 获取当前天气数据（调用真实API）
                const currentRes = await fetch(`${BASE_URL}/weather?q=${encodeURIComponent(city)}&units=metric&appid=${API_KEY}&lang=zh_cn`);
                if (!currentRes.ok) throw new Error(`城市 "${city}" 未找到`);
                const currentData = await currentRes.json();

                // 2. 获取5天预报数据
                const forecastRes = await fetch(`${BASE_URL}/forecast?q=${encodeURIComponent(city)}&units=metric&appid=${API_KEY}&lang=zh_cn`);
                const forecastData = await forecastRes.json();

                // 3. 显示数据
                displayWeather(currentData);
                displayForecast(forecastData);

            } catch (error) {
                showError('获取失败：' + error.message);
            }
        }
        
        // 通过地理位置获取天气
        function getLocationWeather() {
            if (!navigator.geolocation) { showError('浏览器不支持地理位置'); return; }
            
            weatherContainer.innerHTML = `<div class="loading"><i class="fas fa-spinner"></i><p>正在获取位置...</p></div>`;
            
            navigator.geolocation.getCurrentPosition(
                async (pos) => {
                    const { latitude, longitude } = pos.coords;
                    try {
                        // 调用API：根据经纬度获取天气
                        const currentRes = await fetch(`${BASE_URL}/weather?lat=${latitude}&lon=${longitude}&units=metric&appid=${API_KEY}&lang=zh_cn`);
                        const forecastRes = await fetch(`${BASE_URL}/forecast?lat=${latitude}&lon=${longitude}&units=metric&appid=${API_KEY}&lang=zh_cn`);
                        
                        const currentData = await currentRes.json();
                        const forecastData = await forecastRes.json();
                        
                        displayWeather(currentData);
                        displayForecast(forecastData);
                        cityInput.value = currentData.name; // 更新输入框为当前城市
                    } catch (error) {
                        showError('位置获取失败：' + error.message);
                    }
                },
                (error) => showError('位置权限被拒绝：' + error.message)
            );
        }
        
        // 显示当前天气
        function displayWeather(data) {
            const { name, sys, main, wind, weather, visibility, dt } = data;
            const cityName = `${name}, ${sys.country}`;
            const temp = Math.round(main.temp);
            const feelsLike = Math.round(main.feels_like);
            const humidity = main.humidity;
            const pressure = main.pressure;
            const windSpeed = wind.speed;
            const weatherDesc = weather[0].description;
            const icon = weather[0].icon;
            const date = new Date(dt * 1000).toLocaleDateString('zh-CN', { 
                year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' 
            });

            weatherContainer.innerHTML = `
                <div class="weather-card">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="weather-city">${cityName}</div>
                        <div>${date}</div>
                    </div>
                    <div style="display: flex; align-items: center; margin: 20px 0;">
                        <div style="font-size: 4rem; margin-right: 20px;"><i class="${weatherIcons[icon]}"></i></div>
                        <div>
                            <div class="temperature">${temp}°C</div>
                            <div style="font-size: 1.4rem;">${weatherDesc}</div>
                            <div>体感温度: ${feelsLike}°C</div>
                        </div>
                    </div>
                    <div class="weather-details">
                        <div class="detail-item"><i class="fas fa-tint"></i> <div><div>湿度</div><div>${humidity}%</div></div></div>
                        <div class="detail-item"><i class="fas fa-wind"></i> <div><div>风速</div><div>${windSpeed} m/s</div></div></div>
                        <div class="detail-item"><i class="fas fa-compress-alt"></i> <div><div>气压</div><div>${pressure} hPa</div></div></div>
                        <div class="detail-item"><i class="fas fa-eye"></i> <div><div>能见度</div><div>${(visibility/1000).toFixed(1)} km</div></div></div>
                    </div>
                </div>
                <div><h3 style="margin: 20px 0;">未来5天预报</h3><div class="forecast-grid" id="forecastGrid"></div></div>
            `;
        }
        
        // 显示5天预报
        function displayForecast(data) {
            const forecastGrid = document.getElementById('forecastGrid');
            if (!forecastGrid) return;
            
            // 过滤：每8小时1次数据，取每天12点左右的预报
            const dailyForecasts = data.list.filter(item => item.dt_txt.includes('12:00')).slice(0, 5);
            
            forecastGrid.innerHTML = dailyForecasts.map(item => {
                const date = new Date(item.dt * 1000);
                return `
                    <div class="forecast-item">
                        <div style="font-weight: 600;">${weekdays[date.getDay()]}</div>
                        <div>${date.getMonth()+1}月${date.getDate()}日</div>
                        <div style="font-size: 2.5rem; margin: 10px 0;"><i class="${weatherIcons[item.weather[0].icon]}"></i></div>
                        <div>${item.weather[0].description}</div>
                        <div style="margin-top: 10px;">
                            <span style="color: #e74c3c;">${Math.round(item.main.temp_max)}°</span>
                            <span style="color: #3498db; margin-left: 10px;">${Math.round(item.main.temp_min)}°</span>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 显示错误
        function showError(text) {
            errorText.textContent = text;
            errorMessage.style.display = 'block';
        }
        
        // 隐藏错误
        function hideError() {
            errorMessage.style.display = 'none';
        }
    </script>
</body>
</html>

