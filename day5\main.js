// main.js - 主应用入口，使用ES6+模块化
import TaskManager from './TaskManager.js';

// 使用const声明常量（ES6特性）
const taskManager = new TaskManager();

// 获取DOM元素
const taskInput = document.getElementById('taskInput');
const prioritySelect = document.getElementById('prioritySelect');
const addTaskBtn = document.getElementById('addTaskBtn');
const taskList = document.getElementById('taskList');
const filterBtns = document.querySelectorAll('.filter-btn');

// 统计元素
const totalTasksEl = document.getElementById('totalTasks');
const completedTasksEl = document.getElementById('completedTasks');
const pendingTasksEl = document.getElementById('pendingTasks');

// 当前过滤器状态
let currentFilter = 'all';

// 初始化应用
function initApp() {
    renderTasks();
    updateStats();
    setupEventListeners();
}

// 设置事件监听器
function setupEventListeners() {
    // 添加任务按钮事件
    addTaskBtn.addEventListener('click', handleAddTask);
    
    // 输入框回车事件
    taskInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleAddTask();
        }
    });
    
    // 过滤器按钮事件 - 使用箭头函数（ES6特性）
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // 移除所有active类
            filterBtns.forEach(b => b.classList.remove('active'));
            // 添加active类到当前按钮
            btn.classList.add('active');
            
            currentFilter = btn.dataset.filter;
            renderTasks();
        });
    });
    
    // 任务列表事件委托
    taskList.addEventListener('click', handleTaskListClick);
}

// 处理添加任务
function handleAddTask() {
    const text = taskInput.value.trim();
    const priority = prioritySelect.value;
    
    if (!text) {
        alert('请输入任务内容！');
        return;
    }
    
    try {
        taskManager.addTask(text, priority);
        
        // 清空输入框
        taskInput.value = '';
        
        // 重新渲染
        renderTasks();
        updateStats();
        
        // 显示成功提示
        showNotification('任务添加成功！', 'success');
        
    } catch (error) {
        alert('添加任务失败: ' + error.message);
    }
}

// 处理任务列表点击事件
function handleTaskListClick(e) {
    const taskItem = e.target.closest('.task-item');
    if (!taskItem) return;
    
    // 从data属性获取任务ID（需要转换为Symbol）
    const taskIdString = taskItem.dataset.taskId;
    const task = taskManager.getAllTasks().find(t => t.id.toString() === taskIdString);
    
    if (!task) return;
    
    // 处理复选框点击
    if (e.target.classList.contains('task-checkbox')) {
        taskManager.toggleTaskComplete(task.id);
        renderTasks();
        updateStats();
        return;
    }
    
    // 处理删除按钮点击
    if (e.target.closest('.delete-btn')) {
        if (confirm('确定要删除这个任务吗？')) {
            taskManager.deleteTask(task.id);
            renderTasks();
            updateStats();
            showNotification('任务已删除', 'info');
        }
        return;
    }
}

// 渲染任务列表
function renderTasks() {
    const tasks = taskManager.filterTasks(currentFilter);
    
    if (tasks.length === 0) {
        // 使用模板字符串显示空状态（ES6特性）
        taskList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <h3>暂无任务</h3>
                <p>${getEmptyStateMessage()}</p>
            </div>
        `;
        return;
    }
    
    // 使用map和join渲染任务列表（ES6特性）
    taskList.innerHTML = tasks
        .sort((a, b) => {
            // 优先级排序：high > medium > low
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        })
        .map(task => task.toHTML())
        .join('');
}

// 获取空状态消息
function getEmptyStateMessage() {
    const messages = {
        all: '开始添加你的第一个任务吧！',
        pending: '太棒了！所有任务都已完成！',
        completed: '还没有完成的任务',
        high: '没有高优先级任务'
    };
    
    return messages[currentFilter] || '没有符合条件的任务';
}

// 更新统计信息
function updateStats() {
    const stats = taskManager.getStats();
    
    // 使用解构赋值更新DOM（ES6特性）
    const { total, completed, pending } = stats;
    
    totalTasksEl.textContent = total;
    completedTasksEl.textContent = completed;
    pendingTasksEl.textContent = pending;
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加样式
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '600',
        zIndex: '1000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease'
    });
    
    // 根据类型设置背景色
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        info: '#17a2b8'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', initApp);

// 导出供其他模块使用（ES6模块化特性）
export { taskManager, renderTasks, updateStats };