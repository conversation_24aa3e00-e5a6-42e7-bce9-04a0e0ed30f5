// TaskManager.js - 使用ES6+ Map和Set管理任务
import Task from './Task.js';

export default class TaskManager {
    constructor() {
        // 使用Map存储任务（ES6特性）- 键可以是任意类型
        this.tasks = new Map();
        
        // 使用Set存储已完成任务的ID（ES6特性）- 自动去重
        this.completedTaskIds = new Set();
        
        // 加载本地存储的任务
        this.loadFromStorage();
    }
    
    // 添加新任务
    addTask(text, priority = 'medium') {
        if (!text || !text.trim()) {
            throw new Error('任务内容不能为空');
        }
        
        const task = new Task(text.trim(), priority);
        this.tasks.set(task.id, task);
        
        // 保存到本地存储
        this.saveToStorage();
        
        return task;
    }
    
    // 删除任务
    deleteTask(taskId) {
        const deleted = this.tasks.delete(taskId);
        if (deleted) {
            this.completedTaskIds.delete(taskId);
            this.saveToStorage();
        }
        return deleted;
    }
    
    // 切换任务完成状态
    toggleTaskComplete(taskId) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.toggleComplete();
            
            // 更新已完成任务集合
            if (task.completed) {
                this.completedTaskIds.add(taskId);
            } else {
                this.completedTaskIds.delete(taskId);
            }
            
            this.saveToStorage();
            return task;
        }
        return null;
    }
    
    // 获取所有任务（返回数组）
    getAllTasks() {
        // 使用展开运算符将Map的值转换为数组（ES6特性）
        return [...this.tasks.values()];
    }
    
    // 根据条件过滤任务
    filterTasks(filter) {
        const allTasks = this.getAllTasks();
        
        switch (filter) {
            case 'completed':
                return allTasks.filter(task => task.completed);
            case 'pending':
                return allTasks.filter(task => !task.completed);
            case 'high':
                return allTasks.filter(task => task.priority === 'high');
            case 'medium':
                return allTasks.filter(task => task.priority === 'medium');
            case 'low':
                return allTasks.filter(task => task.priority === 'low');
            default:
                return allTasks;
        }
    }
    
    // 获取任务统计信息
    getStats() {
        const allTasks = this.getAllTasks();
        
        return {
            total: allTasks.length,
            completed: this.completedTaskIds.size,
            pending: allTasks.length - this.completedTaskIds.size,
            // 使用对象解构和计算属性（ES6特性）
            byPriority: this.getTasksByPriority()
        };
    }
    
    // 按优先级分组任务
    getTasksByPriority() {
        const allTasks = this.getAllTasks();
        
        // 使用reduce和对象展开运算符（ES6特性）
        return allTasks.reduce((acc, task) => {
            acc[task.priority] = (acc[task.priority] || 0) + 1;
            return acc;
        }, {});
    }
    
    // 清空所有已完成任务
    clearCompletedTasks() {
        // 使用for...of循环遍历Set（ES6特性）
        for (const taskId of this.completedTaskIds) {
            this.tasks.delete(taskId);
        }
        
        this.completedTaskIds.clear();
        this.saveToStorage();
    }
    
    // 保存到本地存储
    saveToStorage() {
        try {
            const tasksData = this.getAllTasks().map(task => task.toJSON());
            localStorage.setItem('es6-tasks', JSON.stringify(tasksData));
        } catch (error) {
            console.error('保存任务失败:', error);
        }
    }
    
    // 从本地存储加载
    loadFromStorage() {
        try {
            const savedData = localStorage.getItem('es6-tasks');
            if (savedData) {
                const tasksData = JSON.parse(savedData);
                
                // 使用forEach重建任务Map
                tasksData.forEach(taskData => {
                    const task = Task.fromJSON(taskData);
                    this.tasks.set(task.id, task);
                    
                    if (task.completed) {
                        this.completedTaskIds.add(task.id);
                    }
                });
            }
        } catch (error) {
            console.error('加载任务失败:', error);
        }
    }
}